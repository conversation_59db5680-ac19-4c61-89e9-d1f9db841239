"""
Document handling and management functionality.
"""
import os
import shutil
import logging
from pathlib import Path
from typing import List, Optional, Dict, Any
from datetime import datetime

from db.database import db_manager
from db.models import Document
from config.settings import get_documents_dir, ALLOWED_EXTENSIONS, MAX_FILE_SIZE

logger = logging.getLogger(__name__)

class DocumentManager:
    """Manages document operations including storage, retrieval, and search."""
    
    def __init__(self):
        """Initialize document manager."""
        self.documents_dir = Path(get_documents_dir())
        self.documents_dir.mkdir(parents=True, exist_ok=True)
    
    def add_document(self, file_path: str, title: str, description: str = "",
                    category: str = "", tags: List[str] = None, user_id: int = None) -> Optional[Document]:
        """Add a new document to the system."""
        try:
            source_path = Path(file_path)
            
            # Validate file
            if not source_path.exists():
                raise FileNotFoundError(f"File not found: {file_path}")
            
            if not self._is_allowed_file_type(source_path):
                raise ValueError(f"File type not allowed: {source_path.suffix}")
            
            file_size = source_path.stat().st_size
            if file_size > MAX_FILE_SIZE:
                raise ValueError(f"File size exceeds maximum allowed size: {file_size} bytes")
            
            # Generate unique filename to avoid conflicts
            file_name = source_path.name
            destination_path = self._get_unique_file_path(file_name)
            
            # Copy file to documents directory
            shutil.copy2(source_path, destination_path)
            
            # Create document record
            document = Document(
                title=title,
                description=description,
                file_name=file_name,
                file_path=str(destination_path),
                file_size=file_size,
                file_type=source_path.suffix.lower(),
                category=category,
                tags="",
                user_id=user_id,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            if tags:
                document.set_tags_list(tags)
            
            # Insert into database
            query = '''
                INSERT INTO documents (title, description, file_name, file_path,
                                     file_size, file_type, category, tags, user_id, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''
            params = (
                document.title, document.description, document.file_name,
                document.file_path, document.file_size, document.file_type,
                document.category, document.tags, document.user_id, document.created_at, document.updated_at
            )
            
            document.id = db_manager.execute_insert(query, params)
            logger.info(f"Document added successfully: {document.title}")
            return document
            
        except Exception as e:
            logger.error(f"Error adding document: {e}")
            # Clean up copied file if database insert failed
            if 'destination_path' in locals() and destination_path.exists():
                destination_path.unlink()
            raise
    
    def get_document(self, document_id: int) -> Optional[Document]:
        """Get a document by ID."""
        try:
            query = "SELECT * FROM documents WHERE id = ?"
            results = db_manager.execute_query(query, (document_id,))
            
            if results:
                return Document.from_dict(results[0])
            return None
            
        except Exception as e:
            logger.error(f"Error getting document {document_id}: {e}")
            return None
    
    def get_all_documents(self, limit: int = 100, offset: int = 0) -> List[Document]:
        """Get all documents with pagination."""
        try:
            query = '''
                SELECT * FROM documents 
                ORDER BY created_at DESC 
                LIMIT ? OFFSET ?
            '''
            results = db_manager.execute_query(query, (limit, offset))
            return [Document.from_dict(row) for row in results]
            
        except Exception as e:
            logger.error(f"Error getting documents: {e}")
            return []
    
    def search_documents(self, search_term: str = "", category: str = "", 
                        limit: int = 100, offset: int = 0) -> List[Document]:
        """Search documents by title, description, or category."""
        try:
            conditions = []
            params = []
            
            if search_term:
                conditions.append("(title LIKE ? OR description LIKE ?)")
                search_pattern = f"%{search_term}%"
                params.extend([search_pattern, search_pattern])
            
            if category:
                conditions.append("category = ?")
                params.append(category)
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            query = f'''
                SELECT * FROM documents 
                WHERE {where_clause}
                ORDER BY created_at DESC 
                LIMIT ? OFFSET ?
            '''
            params.extend([limit, offset])
            
            results = db_manager.execute_query(query, tuple(params))
            return [Document.from_dict(row) for row in results]
            
        except Exception as e:
            logger.error(f"Error searching documents: {e}")
            return []
    
    def delete_document(self, document_id: int) -> bool:
        """Delete a document and its file."""
        try:
            # Get document info first
            document = self.get_document(document_id)
            if not document:
                return False
            
            # Delete file
            file_path = Path(document.file_path)
            if file_path.exists():
                file_path.unlink()
            
            # Delete from database
            query = "DELETE FROM documents WHERE id = ?"
            rows_affected = db_manager.execute_update(query, (document_id,))
            
            if rows_affected > 0:
                logger.info(f"Document deleted successfully: {document.title}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Error deleting document {document_id}: {e}")
            return False
    
    def get_categories(self) -> List[str]:
        """Get all unique categories."""
        try:
            query = "SELECT DISTINCT category FROM documents WHERE category != '' ORDER BY category"
            results = db_manager.execute_query(query)
            return [row['category'] for row in results]
            
        except Exception as e:
            logger.error(f"Error getting categories: {e}")
            return []
    
    def _is_allowed_file_type(self, file_path: Path) -> bool:
        """Check if file type is allowed."""
        return file_path.suffix.lower() in ALLOWED_EXTENSIONS
    
    def _get_unique_file_path(self, file_name: str) -> Path:
        """Generate a unique file path to avoid conflicts."""
        base_path = self.documents_dir / file_name
        
        if not base_path.exists():
            return base_path
        
        # Add counter to filename if it already exists
        name_stem = base_path.stem
        suffix = base_path.suffix
        counter = 1
        
        while True:
            new_name = f"{name_stem}_{counter}{suffix}"
            new_path = self.documents_dir / new_name
            if not new_path.exists():
                return new_path
            counter += 1

# Global document manager instance
document_manager = DocumentManager()
