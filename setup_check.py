#!/usr/bin/env python3
"""
Setup verification script for Document Management System (DMS).
Run this script to verify that your installation is working correctly.
"""

import sys
import os
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible."""
    print("Checking Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - Requires Python 3.8+")
        return False

def check_tkinter():
    """Check if tkinter is available."""
    print("Checking tkinter availability...")
    try:
        import tkinter
        print("✅ tkinter - Available")
        return True
    except ImportError:
        print("❌ tkinter - Not available (required for GUI)")
        return False

def check_project_structure():
    """Check if all required files and directories exist."""
    print("Checking project structure...")
    
    required_files = [
        "main.py",
        "requirements.txt",
        "README.md",
        "config/settings.py",
        "db/database.py",
        "db/models.py",
        "models/document.py",
        "ui/main_window.py",
        "ui/themes.py",
        "tests/test_phase1.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if not missing_files:
        print("✅ Project structure - Complete")
        return True
    else:
        print("❌ Project structure - Missing files:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False

def check_imports():
    """Check if all modules can be imported."""
    print("Checking module imports...")
    
    modules_to_test = [
        ("config.settings", "Configuration"),
        ("db.database", "Database layer"),
        ("db.models", "Database models"),
        ("models.document", "Document management"),
        ("ui.themes", "UI themes"),
        ("ui.main_window", "Main window")
    ]
    
    failed_imports = []
    for module_name, description in modules_to_test:
        try:
            __import__(module_name)
        except ImportError as e:
            failed_imports.append((module_name, description, str(e)))
    
    if not failed_imports:
        print("✅ Module imports - All successful")
        return True
    else:
        print("❌ Module imports - Failed:")
        for module_name, description, error in failed_imports:
            print(f"   - {description} ({module_name}): {error}")
        return False

def check_database_creation():
    """Check if database can be created."""
    print("Checking database creation...")
    try:
        from db.database import db_manager
        db_manager.create_tables()
        print("✅ Database creation - Successful")
        return True
    except Exception as e:
        print(f"❌ Database creation - Failed: {e}")
        return False

def run_quick_test():
    """Run a quick functionality test."""
    print("Running quick functionality test...")
    try:
        # Import test function
        sys.path.insert(0, str(Path(__file__).parent))
        from tests.test_phase1 import test_database_connection, test_document_model
        
        # Run basic tests
        if test_database_connection() and test_document_model():
            print("✅ Quick functionality test - Passed")
            return True
        else:
            print("❌ Quick functionality test - Failed")
            return False
    except Exception as e:
        print(f"❌ Quick functionality test - Error: {e}")
        return False

def main():
    """Main setup check function."""
    print("=" * 60)
    print("Document Management System (DMS) - Setup Verification")
    print("=" * 60)
    
    checks = [
        ("Python Version", check_python_version),
        ("Tkinter Availability", check_tkinter),
        ("Project Structure", check_project_structure),
        ("Module Imports", check_imports),
        ("Database Creation", check_database_creation),
        ("Quick Functionality Test", run_quick_test)
    ]
    
    passed = 0
    failed = 0
    
    for check_name, check_function in checks:
        print(f"\n{check_name}:")
        print("-" * 40)
        try:
            if check_function():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {check_name} - Unexpected error: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"Setup Check Results: {passed} passed, {failed} failed")
    print("=" * 60)
    
    if failed == 0:
        print("\n🎉 Setup verification successful!")
        print("Your DMS installation is ready to use.")
        print("\nTo start the application, run:")
        print("   python main.py")
        print("\nTo run full tests, run:")
        print("   python tests/test_phase1.py")
    else:
        print("\n⚠️  Setup verification failed!")
        print("Please fix the issues above before running the application.")
        print("\nFor help, check the README.md file or the troubleshooting section.")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
