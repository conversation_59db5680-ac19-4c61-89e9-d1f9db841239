#!/usr/bin/env python3
"""
Document Management System (DMS) - Main Application Entry Point

A desktop Electronic Document Management System built with Python and Tkinter.
Features include document upload, metadata management, search functionality,
and file organization.

Author: DMS Development Team
Version: 1.0.0 (Phase 1 - MVP)
"""

import sys
import os
import logging
import tkinter as tk
from tkinter import messagebox
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from config.settings import APP_NAME, APP_VERSION, LOG_FILE, LOG_LEVEL, ensure_directories
    from db.database import db_manager
    from ui.main_window import MainWindow
    from ui.login_window import LoginWindow
    from models.session import session_manager
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Please ensure all required dependencies are installed.")
    sys.exit(1)

def setup_logging():
    """Setup application logging."""
    try:
        ensure_directories()
        
        # Configure logging
        logging.basicConfig(
            level=getattr(logging, LOG_LEVEL.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(LOG_FILE),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        logger = logging.getLogger(__name__)
        logger.info(f"Starting {APP_NAME} v{APP_VERSION}")
        return logger
        
    except Exception as e:
        print(f"Error setting up logging: {e}")
        # Create a basic logger if file logging fails
        logging.basicConfig(level=logging.INFO)
        return logging.getLogger(__name__)

def initialize_database():
    """Initialize the database and create tables."""
    try:
        logger = logging.getLogger(__name__)
        logger.info("Initializing database...")

        # Create database tables
        db_manager.create_tables()

        # Run database migrations
        db_manager.run_migrations()

        logger.info("Database initialized successfully")
        return True

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"Database initialization failed: {e}")
        messagebox.showerror(
            "Database Error",
            f"Failed to initialize database:\n{e}\n\nThe application will now exit."
        )
        return False

def authenticate_user():
    """Handle user authentication."""
    try:
        logger = logging.getLogger(__name__)
        logger.info("Starting user authentication...")

        # Show login window
        login_window = LoginWindow()
        login_result = login_window.show()

        if not login_result:
            logger.info("User authentication cancelled")
            return None

        logger.info(f"User authenticated: {login_result['user'].username}")
        return login_result

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"Authentication error: {e}")
        messagebox.showerror(
            "Authentication Error",
            f"Authentication failed:\n{e}\n\nThe application will now exit."
        )
        return None

def create_application(login_result):
    """Create and configure the main application."""
    try:
        logger = logging.getLogger(__name__)
        logger.info("Creating main application window...")

        # Create root window
        root = tk.Tk()

        # Create main application window with authenticated user
        app = MainWindow(root, login_result['user'])

        logger.info("Application created successfully")
        return root, app

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to create application: {e}")
        messagebox.showerror(
            "Application Error",
            f"Failed to create application:\n{e}\n\nThe application will now exit."
        )
        return None, None

def main():
    """Main application entry point."""
    try:
        # Setup logging
        logger = setup_logging()
        
        # Initialize database
        if not initialize_database():
            return 1

        # Authenticate user
        login_result = authenticate_user()
        if not login_result:
            return 0  # User cancelled login

        # Create application
        root, app = create_application(login_result)
        if not root or not app:
            return 1
        
        # Setup exception handling for the GUI
        def handle_exception(exc_type, exc_value, exc_traceback):
            """Handle uncaught exceptions in the GUI."""
            if issubclass(exc_type, KeyboardInterrupt):
                sys.__excepthook__(exc_type, exc_value, exc_traceback)
                return
            
            logger.error(
                "Uncaught exception",
                exc_info=(exc_type, exc_value, exc_traceback)
            )
            
            messagebox.showerror(
                "Unexpected Error",
                f"An unexpected error occurred:\n{exc_value}\n\nPlease check the log file for details."
            )
        
        # Set the exception handler
        sys.excepthook = handle_exception
        
        # Setup graceful shutdown
        def on_closing():
            """Handle application closing."""
            try:
                logger.info("Application shutting down...")

                # Logout user
                if session_manager.is_authenticated():
                    session_manager.logout()
                    logger.info("User logged out")

                root.quit()
                root.destroy()
            except Exception as e:
                logger.error(f"Error during shutdown: {e}")
            finally:
                sys.exit(0)
        
        root.protocol("WM_DELETE_WINDOW", on_closing)
        
        # Start the application
        logger.info("Starting main event loop...")
        root.mainloop()
        
        return 0
        
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
        return 0
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"Fatal error: {e}")
        print(f"Fatal error: {e}")
        return 1

if __name__ == "__main__":
    """Entry point when script is run directly."""
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"Critical error: {e}")
        sys.exit(1)
