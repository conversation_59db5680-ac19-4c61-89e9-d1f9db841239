"""
Main application window for the Document Management System.
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import subprocess
import platform
from typing import List, Optional

from config.settings import WINDOW_WIDTH, WINDOW_HEIGHT, APP_NAME
from ui.themes import theme_manager
from ui.user_management import UserProfileWindow
from ui.admin_panel import AdminPanel
from models.document import document_manager
from models.session import session_manager
from db.models import Document, User

class MainWindow:
    """Main application window."""
    
    def __init__(self, root: tk.Tk, user: User):
        """Initialize the main window."""
        self.root = root
        self.user = user
        self.documents: List[Document] = []
        self.current_search = ""
        self.current_category = ""

        self._setup_window()
        self._create_widgets()
        self._load_documents()
    
    def _setup_window(self):
        """Setup the main window properties."""
        self.root.title(f"{APP_NAME} - {self.user.full_name} ({self.user.role})")
        self.root.geometry(f"{WINDOW_WIDTH}x{WINDOW_HEIGHT}")
        self.root.minsize(800, 600)

        # Center the window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (WINDOW_WIDTH // 2)
        y = (self.root.winfo_screenheight() // 2) - (WINDOW_HEIGHT // 2)
        self.root.geometry(f"{WINDOW_WIDTH}x{WINDOW_HEIGHT}+{x}+{y}")

        # Apply theme
        theme_manager.apply_theme(self.root)
    
    def _create_widgets(self):
        """Create and layout all widgets."""
        # Main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create menu bar
        self._create_menu_bar()

        # Create toolbar
        self._create_toolbar(main_frame)

        # Create search frame
        self._create_search_frame(main_frame)

        # Create documents list
        self._create_documents_list(main_frame)

        # Create status bar
        self._create_status_bar(main_frame)

    def _create_menu_bar(self):
        """Create the menu bar."""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)

        if self.user.has_permission("write"):
            file_menu.add_command(label="Add Document", command=self._add_document)
            file_menu.add_separator()

        file_menu.add_command(label="Refresh", command=self._refresh_documents)
        file_menu.add_separator()
        file_menu.add_command(label="Logout", command=self._logout)
        file_menu.add_command(label="Exit", command=self.root.quit)

        # User menu
        user_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="User", menu=user_menu)
        user_menu.add_command(label="Profile", command=self._show_profile)

        # Admin menu (only for admins)
        if self.user.has_permission("user_management"):
            admin_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="Admin", menu=admin_menu)
            admin_menu.add_command(label="Manage Users", command=self._manage_users)
            admin_menu.add_command(label="View Audit Logs", command=self._view_audit_logs)

        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self._show_about)

    def _create_toolbar(self, parent):
        """Create the toolbar with action buttons."""
        toolbar_frame = ttk.Frame(parent, style='Toolbar.TFrame')
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Upload Document button (only if user has write permission)
        if self.user.has_permission("write"):
            self.upload_btn = ttk.Button(
                toolbar_frame,
                text="📤 Upload Document",
                style='Primary.TButton',
                command=self._upload_document
            )
            self.upload_btn.pack(side=tk.LEFT, padx=(0, 5))

            # Add Document button (alternative method)
            self.add_btn = ttk.Button(
                toolbar_frame,
                text="📁 Add Document",
                style='Secondary.TButton',
                command=self._add_document
            )
            self.add_btn.pack(side=tk.LEFT, padx=(0, 5))

        # Delete Document button (only if user has delete permission)
        if self.user.has_permission("delete"):
            self.delete_btn = ttk.Button(
                toolbar_frame,
                text="🗑️ Delete",
                style='Danger.TButton',
                command=self._delete_document,
                state=tk.DISABLED
            )
            self.delete_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # Open Document button
        self.open_btn = ttk.Button(
            toolbar_frame,
            text="📖 Open",
            style='Success.TButton',
            command=self._open_document,
            state=tk.DISABLED
        )
        self.open_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # Cancel Operation button (initially hidden)
        self.cancel_operation_btn = ttk.Button(
            toolbar_frame,
            text="❌ Cancel Operation",
            style='Warning.TButton',
            command=self._cancel_current_operation,
            state=tk.DISABLED
        )
        self.cancel_operation_btn.pack(side=tk.RIGHT, padx=(0, 5))
        self.cancel_operation_btn.pack_forget()  # Hide initially

        # Refresh button
        self.refresh_btn = ttk.Button(
            toolbar_frame,
            text="🔄 Refresh",
            command=self._refresh_documents
        )
        self.refresh_btn.pack(side=tk.RIGHT)
    
    def _create_search_frame(self, parent):
        """Create the search and filter frame."""
        search_frame = ttk.Frame(parent, style='Card.TFrame')
        search_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Search label and entry
        ttk.Label(search_frame, text="Search:").grid(row=0, column=0, padx=10, pady=10, sticky=tk.W)
        
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(
            search_frame,
            textvariable=self.search_var,
            style='Search.TEntry',
            width=30
        )
        self.search_entry.grid(row=0, column=1, padx=(0, 10), pady=10, sticky=tk.W)
        self.search_entry.bind('<KeyRelease>', self._on_search_change)
        
        # Category filter
        ttk.Label(search_frame, text="Category:").grid(row=0, column=2, padx=10, pady=10, sticky=tk.W)
        
        self.category_var = tk.StringVar()
        self.category_combo = ttk.Combobox(
            search_frame,
            textvariable=self.category_var,
            state="readonly",
            width=20
        )
        self.category_combo.grid(row=0, column=3, padx=(0, 10), pady=10, sticky=tk.W)
        self.category_combo.bind('<<ComboboxSelected>>', self._on_category_change)
        
        # Search button
        search_btn = ttk.Button(
            search_frame,
            text="🔍 Search",
            command=self._search_documents
        )
        search_btn.grid(row=0, column=4, padx=10, pady=10)
        
        # Clear button
        clear_btn = ttk.Button(
            search_frame,
            text="Clear",
            command=self._clear_search
        )
        clear_btn.grid(row=0, column=5, padx=(0, 10), pady=10)
    
    def _create_documents_list(self, parent):
        """Create the documents list with treeview."""
        # Frame for treeview and scrollbar
        list_frame = ttk.Frame(parent)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create treeview
        columns = ('Title', 'Category', 'File Type', 'Size', 'Created')
        self.tree = ttk.Treeview(
            list_frame,
            columns=columns,
            show='headings',
            style='Documents.Treeview'
        )
        
        # Configure columns
        self.tree.heading('Title', text='Title')
        self.tree.heading('Category', text='Category')
        self.tree.heading('File Type', text='Type')
        self.tree.heading('Size', text='Size')
        self.tree.heading('Created', text='Created')
        
        self.tree.column('Title', width=300)
        self.tree.column('Category', width=150)
        self.tree.column('File Type', width=100)
        self.tree.column('Size', width=100)
        self.tree.column('Created', width=150)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack treeview and scrollbar
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Bind events
        self.tree.bind('<<TreeviewSelect>>', self._on_document_select)
        self.tree.bind('<Double-1>', self._on_document_double_click)
    
    def _create_status_bar(self, parent):
        """Create the status bar."""
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        
        status_bar = ttk.Label(
            parent,
            textvariable=self.status_var,
            relief=tk.SUNKEN,
            anchor=tk.W
        )
        status_bar.pack(fill=tk.X, pady=(10, 0))
    
    def _load_documents(self):
        """Load documents from database."""
        try:
            self.documents = document_manager.get_all_documents()
            self._update_documents_list()
            self._update_categories()
            self.status_var.set(f"Loaded {len(self.documents)} documents")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load documents: {e}")
            self.status_var.set("Error loading documents")
    
    def _update_documents_list(self):
        """Update the documents treeview."""
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # Add documents
        for doc in self.documents:
            created_date = doc.created_at.strftime("%Y-%m-%d %H:%M") if doc.created_at else ""
            self.tree.insert('', tk.END, values=(
                doc.title,
                doc.category,
                doc.file_type.upper(),
                doc.get_file_size_formatted(),
                created_date
            ), tags=(str(doc.id),))
    
    def _update_categories(self):
        """Update the category combobox."""
        categories = document_manager.get_categories()
        self.category_combo['values'] = ['All'] + categories
        if not self.category_var.get():
            self.category_var.set('All')
    
    def _on_search_change(self, event):
        """Handle search entry changes."""
        # Implement real-time search with a small delay
        self.root.after(500, self._search_documents)
    
    def _on_category_change(self, event):
        """Handle category selection changes."""
        self._search_documents()
    
    def _search_documents(self):
        """Search documents based on current filters."""
        search_term = self.search_var.get().strip()
        category = self.category_var.get() if self.category_var.get() != 'All' else ""
        
        try:
            self.documents = document_manager.search_documents(search_term, category)
            self._update_documents_list()
            self.status_var.set(f"Found {len(self.documents)} documents")
        except Exception as e:
            messagebox.showerror("Error", f"Search failed: {e}")
            self.status_var.set("Search error")
    
    def _clear_search(self):
        """Clear search filters and reload all documents."""
        self.search_var.set("")
        self.category_var.set("All")
        self._load_documents()
    
    def _refresh_documents(self):
        """Refresh the documents list."""
        self._load_documents()
    
    def _on_document_select(self, event):
        """Handle document selection."""
        selection = self.tree.selection()
        if selection:
            # Enable delete button only if user has delete permission
            if hasattr(self, 'delete_btn') and self.user.has_permission("delete"):
                self.delete_btn.config(state=tk.NORMAL)
            self.open_btn.config(state=tk.NORMAL)
        else:
            if hasattr(self, 'delete_btn'):
                self.delete_btn.config(state=tk.DISABLED)
            self.open_btn.config(state=tk.DISABLED)
    
    def _on_document_double_click(self, event):
        """Handle double-click on document."""
        self._open_document()
    
    def _get_selected_document(self) -> Optional[Document]:
        """Get the currently selected document."""
        selection = self.tree.selection()
        if not selection:
            return None

        item = self.tree.item(selection[0])
        doc_id = int(item['tags'][0])

        for doc in self.documents:
            if doc.id == doc_id:
                return doc
        return None

    def _upload_document(self):
        """Upload a new document with enhanced UI feedback."""
        # Check permission
        if not self.user.has_permission("write"):
            messagebox.showerror("Permission Denied", "You don't have permission to upload documents.")
            return

        # Initialize operation state
        self.operation_cancelled = False

        # Update status and disable button during operation
        self.status_var.set("Selecting file for upload...")
        self.upload_btn.config(state=tk.DISABLED, text="📤 Selecting...")
        self._show_cancel_button()
        self.root.update()

        try:
            # Check for cancellation
            if getattr(self, 'operation_cancelled', False):
                return

            # Open file dialog with enhanced file type support
            file_path = filedialog.askopenfilename(
                title="Select Document to Upload",
                filetypes=[
                    ("All Supported", "*.pdf;*.doc;*.docx;*.txt;*.jpg;*.jpeg;*.png;*.gif;*.bmp"),
                    ("PDF files", "*.pdf"),
                    ("Word documents", "*.doc;*.docx"),
                    ("Text files", "*.txt"),
                    ("Image files", "*.jpg;*.jpeg;*.png;*.gif;*.bmp"),
                    ("All files", "*.*")
                ]
            )

            if not file_path or getattr(self, 'operation_cancelled', False):
                self.status_var.set("Upload cancelled by user")
                return

            # Update status for metadata entry
            self.status_var.set("Enter document details...")
            self.upload_btn.config(text="📤 Processing...")
            self.root.update()

            # Check for cancellation before dialog
            if getattr(self, 'operation_cancelled', False):
                return

            # Open enhanced document details dialog
            dialog = EnhancedDocumentDetailsDialog(self.root, file_path, "Upload")

            # Check for cancellation after dialog
            if getattr(self, 'operation_cancelled', False):
                return

            if dialog.result and not dialog.cancelled:
                # Update status for upload process
                self.status_var.set("Uploading document...")
                self.upload_btn.config(text="📤 Uploading...")
                self.root.update()

                # Check for cancellation before upload
                if getattr(self, 'operation_cancelled', False):
                    return

                document = document_manager.add_document(
                    file_path=file_path,
                    title=dialog.result['title'],
                    description=dialog.result['description'],
                    category=dialog.result['category'],
                    tags=dialog.result['tags'],
                    user_id=self.user.id
                )

                if document and not getattr(self, 'operation_cancelled', False):
                    self._refresh_documents()
                    self.status_var.set(f"Document '{document.title}' uploaded successfully")
                    messagebox.showinfo("Upload Successful", f"Document '{document.title}' has been uploaded successfully!")
                elif not getattr(self, 'operation_cancelled', False):
                    self.status_var.set("Upload failed")
                    messagebox.showerror("Upload Failed", "Failed to upload document. Please try again.")

            else:
                self.status_var.set("Upload cancelled" if dialog.cancelled else "Upload cancelled by user")

        except Exception as e:
            if not getattr(self, 'operation_cancelled', False):
                messagebox.showerror("Upload Error", f"Failed to upload document: {e}")
                self.status_var.set("Upload error occurred")
        finally:
            # Re-enable button and reset text
            self.upload_btn.config(state=tk.NORMAL, text="📤 Upload Document")
            self._hide_cancel_button()

    def _add_document(self):
        """Add a new document (legacy method)."""
        # Check permission
        if not self.user.has_permission("write"):
            messagebox.showerror("Permission Denied", "You don't have permission to add documents.")
            return

        # Open file dialog
        file_path = filedialog.askopenfilename(
            title="Select Document",
            filetypes=[
                ("All Supported", "*.pdf;*.doc;*.docx;*.txt;*.jpg;*.jpeg;*.png;*.gif;*.bmp"),
                ("PDF files", "*.pdf"),
                ("Word documents", "*.doc;*.docx"),
                ("Text files", "*.txt"),
                ("Image files", "*.jpg;*.jpeg;*.png;*.gif;*.bmp"),
                ("All files", "*.*")
            ]
        )

        if not file_path:
            self.status_var.set("File selection cancelled")
            return

        # Open document details dialog
        dialog = DocumentDetailsDialog(self.root, file_path)
        if dialog.result:
            try:
                document = document_manager.add_document(
                    file_path=file_path,
                    title=dialog.result['title'],
                    description=dialog.result['description'],
                    category=dialog.result['category'],
                    tags=dialog.result['tags'],
                    user_id=self.user.id  # Associate document with current user
                )

                if document:
                    self._refresh_documents()
                    self.status_var.set(f"Document '{document.title}' added successfully")
                    messagebox.showinfo("Success", "Document added successfully!")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to add document: {e}")
                self.status_var.set("Error adding document")
        else:
            self.status_var.set("Document addition cancelled")

    def _delete_document(self):
        """Delete the selected document."""
        # Check permission
        if not self.user.has_permission("delete"):
            messagebox.showerror("Permission Denied", "You don't have permission to delete documents.")
            return

        document = self._get_selected_document()
        if not document:
            return

        # Check if user can delete this specific document
        # Non-admin users can only delete their own documents
        if not self.user.is_admin() and document.user_id != self.user.id:
            messagebox.showerror("Permission Denied", "You can only delete your own documents.")
            return

        # Confirm deletion
        result = messagebox.askyesno(
            "Confirm Delete",
            f"Are you sure you want to delete '{document.title}'?\n\nThis will permanently remove the file and cannot be undone."
        )

        if result:
            try:
                if document_manager.delete_document(document.id):
                    self._refresh_documents()
                    self.status_var.set(f"Document '{document.title}' deleted successfully")
                    messagebox.showinfo("Success", "Document deleted successfully!")
                else:
                    messagebox.showerror("Error", "Failed to delete document")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to delete document: {e}")
                self.status_var.set("Error deleting document")

    def _open_document(self):
        """Open the selected document."""
        document = self._get_selected_document()
        if not document:
            return

        try:
            file_path = document.file_path
            if not os.path.exists(file_path):
                messagebox.showerror("Error", "File not found. It may have been moved or deleted.")
                return

            # Open file with default application
            if platform.system() == 'Darwin':  # macOS
                subprocess.call(('open', file_path))
            elif platform.system() == 'Windows':  # Windows
                os.startfile(file_path)
            else:  # Linux
                subprocess.call(('xdg-open', file_path))

            self.status_var.set(f"Opened '{document.title}'")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to open document: {e}")
            self.status_var.set("Error opening document")

    # New menu methods for Phase 2
    def _logout(self):
        """Logout current user."""
        result = messagebox.askyesno("Logout", "Are you sure you want to logout?")
        if result:
            session_manager.logout()
            self.root.quit()

    def _show_profile(self):
        """Show user profile window."""
        profile_window = UserProfileWindow(self.root, self.user)
        if profile_window.result:
            # Refresh user data if profile was updated
            updated_user = session_manager.get_current_user()
            if updated_user:
                self.user = updated_user
                self.root.title(f"{APP_NAME} - {self.user.full_name} ({self.user.role})")

    def _manage_users(self):
        """Show user management window (admin only)."""
        if not self.user.has_permission("user_management"):
            messagebox.showerror("Permission Denied", "You don't have permission to manage users.")
            return

        AdminPanel(self.root)

    def _view_audit_logs(self):
        """Show audit logs window (admin only)."""
        if not self.user.has_permission("user_management"):
            messagebox.showerror("Permission Denied", "You don't have permission to view audit logs.")
            return

        messagebox.showinfo("Coming Soon", "Audit logs viewer will be available in a future update.")

    def _show_about(self):
        """Show about dialog."""
        about_text = f"""
{APP_NAME}
Version 2.0.0 (Phase 2)

A desktop Electronic Document Management System
built with Python and Tkinter.

Features:
• Document upload and management
• User authentication and authorization
• Role-based access control
• Search and categorization
• Secure file storage

Current User: {self.user.full_name}
Role: {self.user.role}
        """
        messagebox.showinfo("About", about_text.strip())

    def _cancel_current_operation(self):
        """Cancel the current operation."""
        self.operation_cancelled = True
        self.cancel_operation_btn.pack_forget()
        self.status_var.set("Operation cancelled by user")

        # Re-enable upload button if it was disabled
        if hasattr(self, 'upload_btn'):
            self.upload_btn.config(state=tk.NORMAL, text="📤 Upload Document")
        if hasattr(self, 'add_btn'):
            self.add_btn.config(state=tk.NORMAL)

    def _show_cancel_button(self):
        """Show the cancel operation button."""
        self.operation_cancelled = False
        self.cancel_operation_btn.pack(side=tk.RIGHT, padx=(0, 5), before=self.refresh_btn)
        self.cancel_operation_btn.config(state=tk.NORMAL)

    def _hide_cancel_button(self):
        """Hide the cancel operation button."""
        self.cancel_operation_btn.pack_forget()


class EnhancedDocumentDetailsDialog:
    """Enhanced dialog for entering document details with better UX."""

    def __init__(self, parent, file_path: str, action: str = "Add"):
        """Initialize the enhanced dialog."""
        self.result = None
        self.file_path = file_path
        self.action = action
        self.cancelled = False

        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(f"{action} Document - Enter Details")
        self.dialog.geometry("450x350")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Handle window close button (X)
        self.dialog.protocol("WM_DELETE_WINDOW", self._on_window_close)

        # Center dialog
        self.dialog.update_idletasks()
        x = parent.winfo_x() + (parent.winfo_width() // 2) - 225
        y = parent.winfo_y() + (parent.winfo_height() // 2) - 175
        self.dialog.geometry(f"450x350+{x}+{y}")

        # Apply theme
        from ui.themes import theme_manager
        theme_manager.apply_theme(self.dialog)

        self._create_widgets()

        # Wait for dialog to close
        self.dialog.wait_window()

    def _on_window_close(self):
        """Handle window close button (X) click."""
        self._cancel_with_confirmation()

    def _create_widgets(self):
        """Create enhanced dialog widgets."""
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Header with file info
        header_frame = ttk.Frame(main_frame, style='Card.TFrame')
        header_frame.pack(fill=tk.X, pady=(0, 15))

        file_name = os.path.basename(self.file_path)
        file_size = os.path.getsize(self.file_path)
        file_size_mb = file_size / (1024 * 1024)

        ttk.Label(
            header_frame,
            text=f"📄 {file_name}",
            font=('Arial', 10, 'bold')
        ).pack(anchor=tk.W, padx=15, pady=(10, 5))

        ttk.Label(
            header_frame,
            text=f"Size: {file_size_mb:.2f} MB",
            font=('Arial', 9)
        ).pack(anchor=tk.W, padx=15, pady=(0, 10))

        # Form fields
        form_frame = ttk.Frame(main_frame)
        form_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # Title
        ttk.Label(form_frame, text="Title: *", font=('Arial', 9, 'bold')).pack(anchor=tk.W)
        self.title_var = tk.StringVar(value=os.path.splitext(file_name)[0])
        self.title_entry = ttk.Entry(form_frame, textvariable=self.title_var, width=55)
        self.title_entry.pack(fill=tk.X, pady=(2, 10))
        self.title_entry.focus()

        # Description
        ttk.Label(form_frame, text="Description:", font=('Arial', 9, 'bold')).pack(anchor=tk.W)
        self.description_text = tk.Text(form_frame, height=4, width=55, wrap=tk.WORD)
        desc_scrollbar = ttk.Scrollbar(form_frame, orient=tk.VERTICAL, command=self.description_text.yview)
        self.description_text.configure(yscrollcommand=desc_scrollbar.set)

        desc_frame = ttk.Frame(form_frame)
        desc_frame.pack(fill=tk.X, pady=(2, 10))
        self.description_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        desc_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Category
        ttk.Label(form_frame, text="Category:", font=('Arial', 9, 'bold')).pack(anchor=tk.W)
        self.category_var = tk.StringVar()
        self.category_combo = ttk.Combobox(form_frame, textvariable=self.category_var, width=52)

        # Load existing categories
        try:
            from models.document import document_manager
            self.category_combo['values'] = document_manager.get_categories()
        except:
            self.category_combo['values'] = ['General', 'Reports', 'Contracts', 'Images']

        self.category_combo.pack(fill=tk.X, pady=(2, 10))

        # Tags
        ttk.Label(form_frame, text="Tags (comma-separated):", font=('Arial', 9, 'bold')).pack(anchor=tk.W)
        self.tags_var = tk.StringVar()
        self.tags_entry = ttk.Entry(form_frame, textvariable=self.tags_var, width=55)
        self.tags_entry.pack(fill=tk.X, pady=(2, 15))

        # Status label for validation messages
        self.status_var = tk.StringVar()
        self.status_label = ttk.Label(
            form_frame,
            textvariable=self.status_var,
            foreground='red',
            font=('Arial', 9)
        )
        self.status_label.pack(pady=(0, 10))

        # Buttons frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        # Cancel button with enhanced styling
        self.cancel_btn = ttk.Button(
            button_frame,
            text="❌ Cancel",
            command=self._cancel_with_confirmation,
            width=15
        )
        self.cancel_btn.pack(side=tk.RIGHT, padx=(10, 0))

        # Action button (Upload/Add)
        self.action_btn = ttk.Button(
            button_frame,
            text=f"📤 {self.action} Document",
            style='Primary.TButton',
            command=self._ok,
            width=18
        )
        self.action_btn.pack(side=tk.RIGHT)

        # Bind Enter key to action button
        self.dialog.bind('<Return>', lambda e: self._ok())
        self.dialog.bind('<Escape>', lambda e: self._cancel_with_confirmation())

    def _cancel_with_confirmation(self):
        """Cancel with user confirmation if data has been entered."""
        # Check if user has entered any data
        has_data = (
            self.title_var.get().strip() != os.path.splitext(os.path.basename(self.file_path))[0] or
            self.description_text.get(1.0, tk.END).strip() or
            self.category_var.get().strip() or
            self.tags_var.get().strip()
        )

        if has_data:
            result = messagebox.askyesno(
                "Confirm Cancel",
                f"Are you sure you want to cancel? All entered information will be lost.\n\nFile: {os.path.basename(self.file_path)}",
                parent=self.dialog
            )
            if not result:
                return

        self.cancelled = True
        self.result = None
        self.dialog.destroy()

    def _ok(self):
        """Handle OK button click with validation."""
        title = self.title_var.get().strip()
        if not title:
            self.status_var.set("⚠️ Title is required")
            self.title_entry.focus()
            return

        # Clear any previous status messages
        self.status_var.set("")

        description = self.description_text.get(1.0, tk.END).strip()
        category = self.category_var.get().strip()
        tags_text = self.tags_var.get().strip()
        tags = [tag.strip() for tag in tags_text.split(',') if tag.strip()] if tags_text else []

        self.result = {
            'title': title,
            'description': description,
            'category': category,
            'tags': tags
        }

        self.dialog.destroy()


class DocumentDetailsDialog:
    """Dialog for entering document details."""

    def __init__(self, parent, file_path: str):
        """Initialize the dialog."""
        self.result = None
        self.file_path = file_path

        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Document Details")
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center dialog
        self.dialog.update_idletasks()
        x = parent.winfo_x() + (parent.winfo_width() // 2) - 200
        y = parent.winfo_y() + (parent.winfo_height() // 2) - 150
        self.dialog.geometry(f"400x300+{x}+{y}")

        self._create_widgets()

        # Wait for dialog to close
        self.dialog.wait_window()

    def _create_widgets(self):
        """Create dialog widgets."""
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # File info
        file_name = os.path.basename(self.file_path)
        ttk.Label(main_frame, text=f"File: {file_name}").pack(anchor=tk.W, pady=(0, 10))

        # Title
        ttk.Label(main_frame, text="Title:").pack(anchor=tk.W)
        self.title_var = tk.StringVar(value=os.path.splitext(file_name)[0])
        title_entry = ttk.Entry(main_frame, textvariable=self.title_var, width=50)
        title_entry.pack(fill=tk.X, pady=(0, 10))
        title_entry.focus()

        # Description
        ttk.Label(main_frame, text="Description:").pack(anchor=tk.W)
        self.description_text = tk.Text(main_frame, height=4, width=50)
        self.description_text.pack(fill=tk.X, pady=(0, 10))

        # Category
        ttk.Label(main_frame, text="Category:").pack(anchor=tk.W)
        self.category_var = tk.StringVar()
        category_combo = ttk.Combobox(main_frame, textvariable=self.category_var, width=47)
        category_combo['values'] = document_manager.get_categories()
        category_combo.pack(fill=tk.X, pady=(0, 10))

        # Tags
        ttk.Label(main_frame, text="Tags (comma-separated):").pack(anchor=tk.W)
        self.tags_var = tk.StringVar()
        tags_entry = ttk.Entry(main_frame, textvariable=self.tags_var, width=50)
        tags_entry.pack(fill=tk.X, pady=(0, 20))

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        # Cancel button with enhanced styling
        self.cancel_btn = ttk.Button(
            button_frame,
            text="❌ Cancel",
            style='Warning.TButton',
            command=self._cancel,
            width=12
        )
        self.cancel_btn.pack(side=tk.RIGHT, padx=(5, 0))

        # Add Document button
        self.add_btn = ttk.Button(
            button_frame,
            text="📁 Add Document",
            style='Primary.TButton',
            command=self._ok,
            width=15
        )
        self.add_btn.pack(side=tk.RIGHT)

        # Bind keyboard shortcuts
        self.dialog.bind('<Return>', lambda e: self._ok())
        self.dialog.bind('<Escape>', lambda e: self._cancel())

    def _ok(self):
        """Handle OK button click."""
        title = self.title_var.get().strip()
        if not title:
            messagebox.showerror("Error", "Title is required")
            return

        description = self.description_text.get(1.0, tk.END).strip()
        category = self.category_var.get().strip()
        tags_text = self.tags_var.get().strip()
        tags = [tag.strip() for tag in tags_text.split(',') if tag.strip()] if tags_text else []

        self.result = {
            'title': title,
            'description': description,
            'category': category,
            'tags': tags
        }

        self.dialog.destroy()

    def _cancel(self):
        """Handle Cancel button click with user feedback."""
        # Provide user feedback about cancellation
        file_name = os.path.basename(self.file_path)
        result = messagebox.askyesno(
            "Confirm Cancel",
            f"Are you sure you want to cancel adding this document?\n\nFile: {file_name}",
            parent=self.dialog
        )

        if result:
            self.result = None
            self.dialog.destroy()
