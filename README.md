# Document Management System (DMS) v2.0

A desktop Electronic Document Management System built with Python and Tkinter, designed for efficient document storage, organization, and retrieval with comprehensive user management and authentication.

## 🎯 Current Version: Phase 2 (User Management & Authentication) - COMPLETE ✅

**Phase 2 is fully functional and tested!** The system now provides secure multi-user document management with role-based access control, user authentication, and comprehensive admin features.

### Core Features
- **Document Upload**: Support for PDF, Word documents, text files, and images
- **Metadata Management**: Title, description, category, and tags for each document
- **Search Functionality**: Search by title, description, or category
- **File Organization**: Secure local file storage with database references
- **Modern UI**: Clean, responsive Tkinter interface with modern themes

### Phase 2 Features (NEW!)
- **User Authentication**: Secure login/logout with bcrypt password hashing
- **Role-Based Access Control**: Admin, Employee, and Viewer roles with granular permissions
- **User Management**: Complete admin interface for managing users and roles
- **Session Management**: Secure token-based sessions with automatic cleanup
- **User Profiles**: Profile management and password change functionality
- **Document Ownership**: Users can manage their own documents with role-based restrictions
- **Audit Logging**: Security event tracking and user action logging

### Enhanced Upload & Cancel Features (LATEST!)
- **📤 Enhanced Upload Button**: Professional upload experience with progress feedback
- **📁 Legacy Add Button**: Traditional document addition method
- **❌ Smart Cancellation**: Cancel operations at any stage with confirmation
- **🎨 Improved UI**: Modern button styles and enhanced dialogs
- **⌨️ Keyboard Shortcuts**: Full keyboard navigation support (Enter/Escape)
- **📊 Real-time Feedback**: Status updates and loading indicators
- **✅ Input Validation**: Smart form validation with error messages

### Supported File Types
- PDF files (`.pdf`)
- Word documents (`.doc`, `.docx`)
- Text files (`.txt`)
- Images (`.jpg`, `.jpeg`, `.png`, `.gif`, `.bmp`)

## Installation and Setup

### Prerequisites
- Python 3.8 or higher
- tkinter (included with Python standard library)
- bcrypt (for secure password hashing)

### Installation Steps

1. **Clone or download the project**:
   ```bash
   git clone <repository-url>
   cd DMSv2
   ```

2. **Create a virtual environment (recommended)**:
   ```bash
   python -m venv venv
   
   # On Windows:
   venv\Scripts\activate
   
   # On macOS/Linux:
   source venv/bin/activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Run the application**:
   ```bash
   python main.py
   ```

5. **First-time setup**:
   - The application will automatically create the database and run migrations
   - A default admin account will be created:
     - Username: `admin`
     - Password: `admin123`
     - **Important**: Change the default password immediately after first login

6. **Verify installation** (optional):
   ```bash
   python setup_check.py
   ```

## Usage

### User Authentication (Phase 2)
1. **First Login**: Use the default admin credentials (admin/admin123)
2. **Change Password**: Immediately change the default password via User → Profile
3. **Create Users**: Use Admin → Manage Users to create additional user accounts
4. **Assign Roles**: Set appropriate roles (Admin, Employee, Viewer) for each user
5. **Logout**: Use File → Logout to securely end your session

### Adding Documents

#### Enhanced Upload Method (Recommended)
1. Click the **📤 Upload Document** button (requires write permission)
2. Select a file from the enhanced file dialog
3. Fill in the document details in the improved dialog:
   - **Title**: Document name (required, auto-filled from filename)
   - **Description**: Optional detailed description with scrollbar
   - **Category**: Select from existing categories or enter new
   - **Tags**: Comma-separated tags for better searchability
4. Click **📤 Upload Document** or press `Enter` to save
5. Use **❌ Cancel** or press `Escape` to cancel (with confirmation if data entered)
6. Monitor progress in the status bar

#### Legacy Add Method
1. Click the **📁 Add Document** button (alternative method)
2. Follow the traditional workflow with standard dialog
3. Documents are automatically associated with the current user

#### Cancellation Options
- **During file selection**: Close the file dialog
- **During details entry**: Click Cancel button or press Escape
- **During upload**: Click the **❌ Cancel Operation** button in toolbar
- **Smart confirmation**: System warns if entered data will be lost

### Searching Documents
- Use the search box to find documents by title or description
- Filter by category using the dropdown menu
- Click "🔍 Search" or press Enter to search
- Click "Clear" to reset filters and show all documents

### Managing Documents
- **View**: Double-click any document to open it with the default application
- **Delete**: Select a document and click "🗑️ Delete"
- **Refresh**: Click "🔄 Refresh" to reload the document list

## Project Structure

```
DMSv2/
├── main.py                 # Main application entry point
├── requirements.txt        # Python dependencies
├── README.md              # This file
├── config/
│   └── settings.py        # Configuration settings
├── ui/                    # User interface components
│   ├── main_window.py     # Main application window
│   └── themes.py          # UI themes and styling
├── db/                    # Database management
│   ├── database.py        # Database connection and setup
│   └── models.py          # Database table definitions
├── models/                # Business logic models
│   └── document.py        # Document handling
└── data/                  # Created automatically
    ├── dms.db            # SQLite database
    └── documents/        # Stored documents
```

## Configuration

The application creates the following directories automatically:
- `data/` - Contains the SQLite database
- `documents/` - Stores uploaded document files
- `logs/` - Application log files

Configuration can be modified in `config/settings.py`:
- Window size and theme
- File size limits
- Allowed file extensions
- Database and storage paths

## Troubleshooting

### Common Issues

1. **"Module not found" errors**:
   - Ensure you're running from the project root directory
   - Check that all files are in the correct locations

2. **Database errors**:
   - The application will create the database automatically
   - Check that you have write permissions in the project directory

3. **File upload errors**:
   - Verify the file type is supported
   - Check that the file size is under 50MB
   - Ensure you have read permissions for the source file

### Log Files
Check `logs/dms.log` for detailed error information and application activity.

## Development Phases

### ✅ Phase 1 (MVP) - Current
- Basic document management
- Simple Tkinter interface
- SQLite database
- File upload and search

### 🔄 Phase 2 - Planned
- User authentication and registration
- Role-based access control
- Session management

### 🔄 Phase 3 - Planned
- Advanced search filters
- Tag system and folders
- Bulk operations

### 🔄 Phase 4 - Planned
- File encryption
- Security enhancements
- Audit trails

### 🔄 Phase 5 - Planned
- Reporting and analytics
- Usage statistics
- Data visualization

### 🔄 Phase 6 - Planned
- Cloud integration
- Backup functionality
- Sync capabilities

## Contributing

This project follows a phased development approach. Each phase builds upon the previous one while maintaining backward compatibility.

## License

[Add your license information here]

## Support

For issues and questions, please check the troubleshooting section above or review the log files for detailed error information.
