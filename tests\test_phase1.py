#!/usr/bin/env python3
"""
Test script for Phase 1 (MVP) functionality.
Tests core document management features without GUI.
"""

import sys
import os
import tempfile
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from db.database import db_manager
from models.document import document_manager
from db.models import Document

def create_test_file(content: str = "Test document content", extension: str = ".txt") -> str:
    """Create a temporary test file."""
    with tempfile.NamedTemporaryFile(mode='w', suffix=extension, delete=False) as f:
        f.write(content)
        return f.name

def test_database_connection():
    """Test database connection and table creation."""
    print("Testing database connection...")
    try:
        db_manager.create_tables()
        
        # Test basic query
        result = db_manager.execute_query("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row['name'] for row in result]
        
        assert 'documents' in tables, "Documents table not found"
        print("✅ Database connection and table creation successful")
        return True
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def test_document_operations():
    """Test document CRUD operations."""
    print("Testing document operations...")
    try:
        # Create test file
        test_file = create_test_file("This is a test document for the DMS system.")
        
        # Test adding document
        document = document_manager.add_document(
            file_path=test_file,
            title="Test Document",
            description="A test document for Phase 1 testing",
            category="Test",
            tags=["test", "phase1", "mvp"]
        )
        
        assert document is not None, "Failed to add document"
        assert document.id is not None, "Document ID not assigned"
        print(f"✅ Document added successfully (ID: {document.id})")
        
        # Test retrieving document
        retrieved_doc = document_manager.get_document(document.id)
        assert retrieved_doc is not None, "Failed to retrieve document"
        assert retrieved_doc.title == "Test Document", "Document title mismatch"
        print("✅ Document retrieval successful")
        
        # Test searching documents
        search_results = document_manager.search_documents("Test")
        assert len(search_results) > 0, "Search returned no results"
        assert any(doc.id == document.id for doc in search_results), "Document not found in search"
        print("✅ Document search successful")
        
        # Test category search
        category_results = document_manager.search_documents(category="Test")
        assert len(category_results) > 0, "Category search returned no results"
        print("✅ Category search successful")
        
        # Test getting all documents
        all_docs = document_manager.get_all_documents()
        assert len(all_docs) > 0, "No documents found"
        print("✅ Get all documents successful")
        
        # Test getting categories
        categories = document_manager.get_categories()
        assert "Test" in categories, "Test category not found"
        print("✅ Get categories successful")
        
        # Test document deletion
        delete_success = document_manager.delete_document(document.id)
        assert delete_success, "Failed to delete document"
        
        # Verify deletion
        deleted_doc = document_manager.get_document(document.id)
        assert deleted_doc is None, "Document still exists after deletion"
        print("✅ Document deletion successful")
        
        # Clean up test file
        os.unlink(test_file)
        
        return True
        
    except Exception as e:
        print(f"❌ Document operations test failed: {e}")
        return False

def test_file_handling():
    """Test file handling and validation."""
    print("Testing file handling...")
    try:
        # Test with different file types
        test_files = [
            ("test.txt", "Text file content"),
            ("test.pdf", "PDF content simulation"),  # Note: not a real PDF
        ]
        
        for filename, content in test_files:
            test_file = create_test_file(content, Path(filename).suffix)
            
            try:
                document = document_manager.add_document(
                    file_path=test_file,
                    title=f"Test {filename}",
                    description=f"Test file for {filename}",
                    category="FileTest"
                )
                
                assert document is not None, f"Failed to add {filename}"
                
                # Verify file was copied to documents directory
                assert os.path.exists(document.file_path), f"Document file not found: {document.file_path}"
                
                # Clean up
                document_manager.delete_document(document.id)
                os.unlink(test_file)
                
                print(f"✅ File handling successful for {filename}")
                
            except Exception as e:
                print(f"❌ File handling failed for {filename}: {e}")
                os.unlink(test_file)
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ File handling test failed: {e}")
        return False

def test_document_model():
    """Test Document model functionality."""
    print("Testing Document model...")
    try:
        # Test Document creation
        doc = Document(
            title="Model Test",
            description="Testing document model",
            file_name="test.txt",
            file_path="/path/to/test.txt",
            file_size=1024,
            file_type=".txt",
            category="ModelTest"
        )
        
        # Test tags functionality
        tags = ["model", "test", "phase1"]
        doc.set_tags_list(tags)
        retrieved_tags = doc.get_tags_list()
        assert retrieved_tags == tags, "Tags mismatch"
        print("✅ Document model tags functionality successful")
        
        # Test file size formatting
        doc.file_size = 1024
        assert "1.0 KB" in doc.get_file_size_formatted(), "File size formatting failed"
        
        doc.file_size = 1024 * 1024
        assert "1.0 MB" in doc.get_file_size_formatted(), "File size formatting failed"
        print("✅ Document model file size formatting successful")
        
        # Test dictionary conversion
        doc_dict = doc.to_dict()
        assert doc_dict['title'] == "Model Test", "Dictionary conversion failed"
        
        # Test from_dict creation
        new_doc = Document.from_dict(doc_dict)
        assert new_doc.title == doc.title, "from_dict creation failed"
        print("✅ Document model dictionary operations successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Document model test failed: {e}")
        return False

def run_all_tests():
    """Run all Phase 1 tests."""
    print("=" * 50)
    print("Running Phase 1 (MVP) Tests")
    print("=" * 50)
    
    tests = [
        test_database_connection,
        test_document_model,
        test_file_handling,
        test_document_operations,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        print(f"\n{test.__name__}:")
        print("-" * 30)
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed} passed, {failed} failed")
    print("=" * 50)
    
    if failed == 0:
        print("🎉 All tests passed! Phase 1 MVP is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
