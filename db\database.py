"""
Database connection and setup for the Document Management System.
"""
import sqlite3
import logging
from pathlib import Path
from typing import Optional, Any, List, Dict
from contextlib import contextmanager

from config.settings import get_database_path

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Manages SQLite database connections and operations."""
    
    def __init__(self, db_path: Optional[str] = None):
        """Initialize database manager with optional custom path."""
        self.db_path = db_path or get_database_path()
        self._ensure_database_exists()
    
    def _ensure_database_exists(self):
        """Ensure the database file and directory exist."""
        db_path = Path(self.db_path)
        db_path.parent.mkdir(parents=True, exist_ok=True)
    
    @contextmanager
    def get_connection(self):
        """Get a database connection with automatic cleanup."""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # Enable column access by name
            yield conn
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            logger.error(f"Database error: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def execute_query(self, query: str, params: tuple = ()) -> List[Dict[str, Any]]:
        """Execute a SELECT query and return results as list of dictionaries."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
    
    def execute_update(self, query: str, params: tuple = ()) -> int:
        """Execute an INSERT, UPDATE, or DELETE query and return affected rows."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            conn.commit()
            return cursor.rowcount
    
    def execute_insert(self, query: str, params: tuple = ()) -> int:
        """Execute an INSERT query and return the last row ID."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            conn.commit()
            return cursor.lastrowid
    
    def create_tables(self):
        """Create all necessary database tables."""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # Create documents table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS documents (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    description TEXT,
                    file_name TEXT NOT NULL,
                    file_path TEXT NOT NULL,
                    file_size INTEGER NOT NULL,
                    file_type TEXT NOT NULL,
                    category TEXT,
                    tags TEXT,
                    user_id INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Create indexes for better search performance
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_documents_title ON documents(title)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_documents_category ON documents(category)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_documents_created_at ON documents(created_at)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_documents_user_id ON documents(user_id)')

            conn.commit()
            logger.info("Database tables created successfully")

    def run_migrations(self):
        """Run database migrations."""
        try:
            # Create migrations table to track applied migrations
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS schema_migrations (
                        version TEXT PRIMARY KEY,
                        applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                conn.commit()

            # Import and run migrations
            from pathlib import Path
            import importlib.util

            migrations_dir = Path(__file__).parent / "migrations"
            if not migrations_dir.exists():
                logger.info("No migrations directory found")
                return

            # Get list of migration files
            migration_files = sorted([f for f in migrations_dir.glob("*.py") if f.name != "__init__.py"])

            for migration_file in migration_files:
                migration_name = migration_file.stem

                # Check if migration already applied
                applied = self.execute_query(
                    "SELECT version FROM schema_migrations WHERE version = ?",
                    (migration_name,)
                )

                if applied:
                    logger.info(f"Migration {migration_name} already applied")
                    continue

                # Load and run migration
                spec = importlib.util.spec_from_file_location(migration_name, migration_file)
                migration_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(migration_module)

                logger.info(f"Running migration {migration_name}")
                migration_module.upgrade(self)

                # Mark migration as applied
                self.execute_update(
                    "INSERT INTO schema_migrations (version) VALUES (?)",
                    (migration_name,)
                )

                logger.info(f"Migration {migration_name} completed successfully")

        except Exception as e:
            logger.error(f"Migration error: {e}")
            raise

# Global database manager instance
db_manager = DatabaseManager()
