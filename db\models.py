"""
Database table definitions and ORM-like models.
"""
from dataclasses import dataclass
from datetime import datetime
from typing import Optional, List, Dict, Any
import json
from enum import Enum

class UserRole(Enum):
    """User role enumeration."""
    ADMIN = "Admin"
    EMPLOYEE = "Employee"
    VIEWER = "Viewer"

    @classmethod
    def get_all_roles(cls) -> List[str]:
        """Get all available roles as strings."""
        return [role.value for role in cls]

    @classmethod
    def has_permission(cls, user_role: str, required_permission: str) -> bool:
        """Check if user role has required permission."""
        permissions = {
            cls.ADMIN.value: ["read", "write", "delete", "admin", "user_management"],
            cls.EMPLOYEE.value: ["read", "write", "delete"],
            cls.VIEWER.value: ["read"]
        }
        return required_permission in permissions.get(user_role, [])

@dataclass
class Document:
    """Document model representing a document in the database."""
    id: Optional[int] = None
    title: str = ""
    description: str = ""
    file_name: str = ""
    file_path: str = ""
    file_size: int = 0
    file_type: str = ""
    category: str = ""
    tags: str = ""
    user_id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Document':
        """Create Document instance from dictionary."""
        return cls(
            id=data.get('id'),
            title=data.get('title', ''),
            description=data.get('description', ''),
            file_name=data.get('file_name', ''),
            file_path=data.get('file_path', ''),
            file_size=data.get('file_size', 0),
            file_type=data.get('file_type', ''),
            category=data.get('category', ''),
            tags=data.get('tags', ''),
            user_id=data.get('user_id'),
            created_at=datetime.fromisoformat(data['created_at']) if data.get('created_at') else None,
            updated_at=datetime.fromisoformat(data['updated_at']) if data.get('updated_at') else None
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert Document instance to dictionary."""
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'file_name': self.file_name,
            'file_path': self.file_path,
            'file_size': self.file_size,
            'file_type': self.file_type,
            'category': self.category,
            'tags': self.tags,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def get_tags_list(self) -> List[str]:
        """Get tags as a list of strings."""
        if not self.tags:
            return []
        try:
            return json.loads(self.tags)
        except json.JSONDecodeError:
            # Fallback for comma-separated tags
            return [tag.strip() for tag in self.tags.split(',') if tag.strip()]
    
    def set_tags_list(self, tags: List[str]):
        """Set tags from a list of strings."""
        self.tags = json.dumps(tags) if tags else ""
    
    def get_file_size_formatted(self) -> str:
        """Get formatted file size string."""
        if self.file_size < 1024:
            return f"{self.file_size} B"
        elif self.file_size < 1024 * 1024:
            return f"{self.file_size / 1024:.1f} KB"
        elif self.file_size < 1024 * 1024 * 1024:
            return f"{self.file_size / (1024 * 1024):.1f} MB"
        else:
            return f"{self.file_size / (1024 * 1024 * 1024):.1f} GB"


@dataclass
class User:
    """User model representing a user in the database."""
    id: Optional[int] = None
    username: str = ""
    email: str = ""
    password_hash: str = ""
    full_name: str = ""
    role: str = UserRole.EMPLOYEE.value
    is_active: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    last_login: Optional[datetime] = None

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'User':
        """Create User instance from dictionary."""
        return cls(
            id=data.get('id'),
            username=data.get('username', ''),
            email=data.get('email', ''),
            password_hash=data.get('password_hash', ''),
            full_name=data.get('full_name', ''),
            role=data.get('role', UserRole.EMPLOYEE.value),
            is_active=bool(data.get('is_active', True)),
            created_at=datetime.fromisoformat(data['created_at']) if data.get('created_at') else None,
            updated_at=datetime.fromisoformat(data['updated_at']) if data.get('updated_at') else None,
            last_login=datetime.fromisoformat(data['last_login']) if data.get('last_login') else None
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert User instance to dictionary."""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'password_hash': self.password_hash,
            'full_name': self.full_name,
            'role': self.role,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }

    def has_permission(self, permission: str) -> bool:
        """Check if user has specific permission."""
        return UserRole.has_permission(self.role, permission)

    def is_admin(self) -> bool:
        """Check if user is admin."""
        return self.role == UserRole.ADMIN.value

    def can_manage_users(self) -> bool:
        """Check if user can manage other users."""
        return self.has_permission("user_management")

    def can_delete_documents(self) -> bool:
        """Check if user can delete documents."""
        return self.has_permission("delete")

    def can_write_documents(self) -> bool:
        """Check if user can create/edit documents."""
        return self.has_permission("write")


@dataclass
class UserSession:
    """User session model for session management."""
    id: Optional[int] = None
    user_id: int = 0
    session_token: str = ""
    created_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    is_active: bool = True

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserSession':
        """Create UserSession instance from dictionary."""
        return cls(
            id=data.get('id'),
            user_id=data.get('user_id', 0),
            session_token=data.get('session_token', ''),
            created_at=datetime.fromisoformat(data['created_at']) if data.get('created_at') else None,
            expires_at=datetime.fromisoformat(data['expires_at']) if data.get('expires_at') else None,
            is_active=bool(data.get('is_active', True))
        )

    def is_expired(self) -> bool:
        """Check if session is expired."""
        if not self.expires_at:
            return True
        return datetime.now() > self.expires_at

    def is_valid(self) -> bool:
        """Check if session is valid (active and not expired)."""
        return self.is_active and not self.is_expired()


@dataclass
class AuditLog:
    """Audit log model for security tracking."""
    id: Optional[int] = None
    user_id: Optional[int] = None
    action: str = ""
    resource_type: str = ""
    resource_id: Optional[int] = None
    details: str = ""
    ip_address: str = ""
    user_agent: str = ""
    created_at: Optional[datetime] = None

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AuditLog':
        """Create AuditLog instance from dictionary."""
        return cls(
            id=data.get('id'),
            user_id=data.get('user_id'),
            action=data.get('action', ''),
            resource_type=data.get('resource_type', ''),
            resource_id=data.get('resource_id'),
            details=data.get('details', ''),
            ip_address=data.get('ip_address', ''),
            user_agent=data.get('user_agent', ''),
            created_at=datetime.fromisoformat(data['created_at']) if data.get('created_at') else None
        )
