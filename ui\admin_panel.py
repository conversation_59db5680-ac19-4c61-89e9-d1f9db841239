"""
Admin panel for user management and system administration.
"""
import tkinter as tk
from tkinter import ttk, messagebox
from typing import List, Optional

from ui.themes import theme_manager
from ui.login_window import RegistrationWindow
from models.user import user_manager
from models.session import session_manager
from db.models import User, UserRole

class AdminPanel:
    """Admin panel for system administration."""
    
    def __init__(self, parent):
        """Initialize admin panel."""
        # Check admin permission
        current_user = session_manager.get_current_user()
        if not current_user or not current_user.has_permission("user_management"):
            messagebox.showerror("Permission Denied", "You don't have permission to access the admin panel.")
            return
        
        self.current_user = current_user
        self.users: List[User] = []
        
        # Create admin window
        self.window = tk.Toplevel(parent)
        self.window.title("Admin Panel - User Management")
        self.window.geometry("800x600")
        self.window.transient(parent)
        self.window.grab_set()
        
        # Center window
        self.window.update_idletasks()
        x = parent.winfo_x() + (parent.winfo_width() // 2) - 400
        y = parent.winfo_y() + (parent.winfo_height() // 2) - 300
        self.window.geometry(f"800x600+{x}+{y}")
        
        # Apply theme
        theme_manager.apply_theme(self.window)
        
        # Create UI
        self._create_widgets()
        self._load_users()
        
        # Wait for window to close
        self.window.wait_window()
    
    def _create_widgets(self):
        """Create admin panel widgets."""
        # Main frame
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Title
        title_label = ttk.Label(
            main_frame,
            text="User Management",
            font=('Arial', 16, 'bold')
        )
        title_label.pack(pady=(0, 20))
        
        # Toolbar
        self._create_toolbar(main_frame)
        
        # Users list
        self._create_users_list(main_frame)
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(
            main_frame,
            textvariable=self.status_var,
            relief=tk.SUNKEN,
            anchor=tk.W
        )
        status_bar.pack(fill=tk.X, pady=(10, 0))
    
    def _create_toolbar(self, parent):
        """Create toolbar with admin actions."""
        toolbar_frame = ttk.Frame(parent, style='Toolbar.TFrame')
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Add User button
        add_user_btn = ttk.Button(
            toolbar_frame,
            text="👤 Add User",
            style='Primary.TButton',
            command=self._add_user
        )
        add_user_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # Edit User button
        self.edit_user_btn = ttk.Button(
            toolbar_frame,
            text="✏️ Edit User",
            command=self._edit_user,
            state=tk.DISABLED
        )
        self.edit_user_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # Toggle Active button
        self.toggle_active_btn = ttk.Button(
            toolbar_frame,
            text="🔄 Toggle Active",
            command=self._toggle_user_active,
            state=tk.DISABLED
        )
        self.toggle_active_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # Refresh button
        refresh_btn = ttk.Button(
            toolbar_frame,
            text="🔄 Refresh",
            command=self._load_users
        )
        refresh_btn.pack(side=tk.RIGHT)
    
    def _create_users_list(self, parent):
        """Create users list with treeview."""
        # Frame for treeview and scrollbar
        list_frame = ttk.Frame(parent)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create treeview
        columns = ('Username', 'Full Name', 'Email', 'Role', 'Status', 'Last Login')
        self.tree = ttk.Treeview(
            list_frame,
            columns=columns,
            show='headings',
            style='Documents.Treeview'
        )
        
        # Configure columns
        self.tree.heading('Username', text='Username')
        self.tree.heading('Full Name', text='Full Name')
        self.tree.heading('Email', text='Email')
        self.tree.heading('Role', text='Role')
        self.tree.heading('Status', text='Status')
        self.tree.heading('Last Login', text='Last Login')
        
        self.tree.column('Username', width=120)
        self.tree.column('Full Name', width=150)
        self.tree.column('Email', width=200)
        self.tree.column('Role', width=100)
        self.tree.column('Status', width=80)
        self.tree.column('Last Login', width=130)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack treeview and scrollbar
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Bind events
        self.tree.bind('<<TreeviewSelect>>', self._on_user_select)
        self.tree.bind('<Double-1>', self._on_user_double_click)
    
    def _load_users(self):
        """Load users from database."""
        try:
            self.users = user_manager.get_all_users(include_inactive=True)
            self._update_users_list()
            self.status_var.set(f"Loaded {len(self.users)} users")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load users: {e}")
            self.status_var.set("Error loading users")
    
    def _update_users_list(self):
        """Update the users treeview."""
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # Add users
        for user in self.users:
            status = "Active" if user.is_active else "Inactive"
            last_login = user.last_login.strftime("%Y-%m-%d %H:%M") if user.last_login else "Never"
            
            self.tree.insert('', tk.END, values=(
                user.username,
                user.full_name,
                user.email,
                user.role,
                status,
                last_login
            ), tags=(str(user.id),))
    
    def _on_user_select(self, event):
        """Handle user selection."""
        selection = self.tree.selection()
        if selection:
            self.edit_user_btn.config(state=tk.NORMAL)
            self.toggle_active_btn.config(state=tk.NORMAL)
        else:
            self.edit_user_btn.config(state=tk.DISABLED)
            self.toggle_active_btn.config(state=tk.DISABLED)
    
    def _on_user_double_click(self, event):
        """Handle double-click on user."""
        self._edit_user()
    
    def _get_selected_user(self) -> Optional[User]:
        """Get the currently selected user."""
        selection = self.tree.selection()
        if not selection:
            return None
        
        item = self.tree.item(selection[0])
        user_id = int(item['tags'][0])
        
        for user in self.users:
            if user.id == user_id:
                return user
        return None
    
    def _add_user(self):
        """Add a new user."""
        register_window = RegistrationWindow(self.window)
        if register_window.result:
            self._load_users()
            self.status_var.set(f"User '{register_window.result['username']}' created successfully")
            messagebox.showinfo("Success", "User created successfully!")
    
    def _edit_user(self):
        """Edit selected user."""
        user = self._get_selected_user()
        if not user:
            return
        
        edit_window = EditUserWindow(self.window, user)
        if edit_window.result:
            self._load_users()
            self.status_var.set(f"User '{user.username}' updated successfully")
    
    def _toggle_user_active(self):
        """Toggle user active status."""
        user = self._get_selected_user()
        if not user:
            return
        
        # Prevent deactivating self
        if user.id == self.current_user.id:
            messagebox.showerror("Error", "You cannot deactivate your own account.")
            return
        
        new_status = not user.is_active
        status_text = "activate" if new_status else "deactivate"
        
        result = messagebox.askyesno(
            "Confirm Action",
            f"Are you sure you want to {status_text} user '{user.username}'?"
        )
        
        if result:
            try:
                success = user_manager.update_user(user.id, is_active=new_status)
                if success:
                    self._load_users()
                    self.status_var.set(f"User '{user.username}' {status_text}d successfully")
                else:
                    messagebox.showerror("Error", f"Failed to {status_text} user")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to {status_text} user: {e}")


class EditUserWindow:
    """Edit user dialog."""
    
    def __init__(self, parent, user: User):
        """Initialize edit user window."""
        self.user = user
        self.result = None
        
        # Create edit window
        self.window = tk.Toplevel(parent)
        self.window.title(f"Edit User - {user.username}")
        self.window.geometry("400x350")
        self.window.resizable(False, False)
        self.window.transient(parent)
        self.window.grab_set()
        
        # Center window
        self.window.update_idletasks()
        x = parent.winfo_x() + (parent.winfo_width() // 2) - 200
        y = parent.winfo_y() + (parent.winfo_height() // 2) - 175
        self.window.geometry(f"400x350+{x}+{y}")
        
        # Apply theme
        theme_manager.apply_theme(self.window)
        
        # Create UI
        self._create_widgets()
        
        # Wait for window to close
        self.window.wait_window()
    
    def _create_widgets(self):
        """Create edit user widgets."""
        # Main frame
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Title
        title_label = ttk.Label(
            main_frame,
            text=f"Edit User: {self.user.username}",
            font=('Arial', 14, 'bold')
        )
        title_label.pack(pady=(0, 20))
        
        # Edit form frame
        form_frame = ttk.Frame(main_frame, style='Card.TFrame')
        form_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Email
        ttk.Label(form_frame, text="Email:").pack(anchor=tk.W, padx=20, pady=(20, 5))
        self.email_var = tk.StringVar(value=self.user.email)
        self.email_entry = ttk.Entry(form_frame, textvariable=self.email_var, width=35)
        self.email_entry.pack(padx=20, pady=(0, 15))
        
        # Full name
        ttk.Label(form_frame, text="Full Name:").pack(anchor=tk.W, padx=20, pady=(0, 5))
        self.full_name_var = tk.StringVar(value=self.user.full_name)
        self.full_name_entry = ttk.Entry(form_frame, textvariable=self.full_name_var, width=35)
        self.full_name_entry.pack(padx=20, pady=(0, 15))
        
        # Role
        ttk.Label(form_frame, text="Role:").pack(anchor=tk.W, padx=20, pady=(0, 5))
        self.role_var = tk.StringVar(value=self.user.role)
        self.role_combo = ttk.Combobox(
            form_frame,
            textvariable=self.role_var,
            values=UserRole.get_all_roles(),
            state="readonly",
            width=32
        )
        self.role_combo.pack(padx=20, pady=(0, 20))
        
        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Cancel button
        cancel_btn = ttk.Button(
            buttons_frame,
            text="Cancel",
            command=self._cancel,
            width=15
        )
        cancel_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        # Save button
        save_btn = ttk.Button(
            buttons_frame,
            text="Save Changes",
            style='Primary.TButton',
            command=self._save_changes,
            width=15
        )
        save_btn.pack(side=tk.RIGHT)
        
        # Status label
        self.status_var = tk.StringVar()
        self.status_label = ttk.Label(
            main_frame,
            textvariable=self.status_var,
            foreground='red',
            font=('Arial', 9)
        )
        self.status_label.pack()
    
    def _save_changes(self):
        """Save user changes."""
        try:
            email = self.email_var.get().strip()
            full_name = self.full_name_var.get().strip()
            role = self.role_var.get()
            
            if not email or not full_name:
                self.status_var.set("Email and full name are required")
                return
            
            if '@' not in email:
                self.status_var.set("Please enter a valid email address")
                return
            
            # Update user
            success = user_manager.update_user(
                user_id=self.user.id,
                email=email,
                full_name=full_name,
                role=role
            )
            
            if success:
                self.result = True
                self.window.destroy()
            else:
                self.status_var.set("Failed to update user")
                
        except ValueError as e:
            self.status_var.set(str(e))
        except Exception as e:
            self.status_var.set(f"Error updating user: {e}")
    
    def _cancel(self):
        """Cancel user editing."""
        self.window.destroy()
