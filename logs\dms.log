2025-09-28 02:58:08,136 - __main__ - INFO - Starting Document Management System v1.0.0
2025-09-28 02:58:08,136 - __main__ - INFO - Initializing database...
2025-09-28 02:58:08,205 - db.database - INFO - Database tables created successfully
2025-09-28 02:58:08,205 - __main__ - INFO - Database initialized successfully
2025-09-28 02:58:08,205 - __main__ - INFO - Creating main application window...
2025-09-28 02:58:10,292 - __main__ - INFO - Application created successfully
2025-09-28 02:58:10,292 - __main__ - INFO - Starting main event loop...
2025-09-28 02:59:28,495 - __main__ - INFO - Application shutting down...
2025-09-28 03:03:25,847 - __main__ - INFO - Starting Document Management System v1.0.0
2025-09-28 03:03:25,848 - __main__ - INFO - Initializing database...
2025-09-28 03:03:25,849 - db.database - INFO - Database tables created successfully
2025-09-28 03:03:25,849 - __main__ - INFO - Database initialized successfully
2025-09-28 03:03:25,849 - __main__ - INFO - Creating main application window...
2025-09-28 03:03:27,005 - __main__ - INFO - Application created successfully
2025-09-28 03:03:27,005 - __main__ - INFO - Starting main event loop...
2025-09-28 03:03:54,674 - __main__ - INFO - Application shutting down...
2025-09-28 03:41:33,788 - __main__ - INFO - Starting Document Management System v1.0.0
2025-09-28 03:41:33,788 - __main__ - INFO - Initializing database...
2025-09-28 03:41:33,789 - db.database - INFO - Database tables created successfully
2025-09-28 03:41:33,793 - db.database - INFO - Migration 001_add_users_table already applied
2025-09-28 03:41:33,793 - __main__ - INFO - Database initialized successfully
2025-09-28 03:41:33,793 - __main__ - INFO - Starting user authentication...
2025-09-28 03:41:51,184 - models.user - WARNING - Authentication failed: user not found - Admin
2025-09-28 03:43:35,838 - __main__ - INFO - Starting Document Management System v1.0.0
2025-09-28 03:43:35,838 - __main__ - INFO - Initializing database...
2025-09-28 03:43:35,839 - db.database - INFO - Database tables created successfully
2025-09-28 03:43:35,842 - db.database - INFO - Migration 001_add_users_table already applied
2025-09-28 03:43:35,842 - __main__ - INFO - Database initialized successfully
2025-09-28 03:43:35,842 - __main__ - INFO - Starting user authentication...
2025-09-28 03:44:31,658 - models.user - INFO - User created successfully: user1
2025-09-28 03:44:47,973 - models.user - INFO - User authenticated successfully: user1
2025-09-28 03:44:47,988 - models.session - INFO - Session created for user user1
2025-09-28 03:44:48,005 - __main__ - INFO - User authenticated: user1
2025-09-28 03:44:48,008 - __main__ - INFO - Creating main application window...
2025-09-28 03:44:49,781 - __main__ - INFO - Application created successfully
2025-09-28 03:44:49,782 - __main__ - INFO - Starting main event loop...
2025-09-28 03:45:35,162 - __main__ - INFO - Application shutting down...
2025-09-28 03:45:35,201 - models.session - INFO - Session invalidated for token aPyltCX2...
2025-09-28 03:45:35,201 - __main__ - INFO - User logged out
2025-09-28 03:54:33,378 - __main__ - INFO - Starting Document Management System v1.0.0
2025-09-28 03:54:33,379 - __main__ - INFO - Initializing database...
2025-09-28 03:54:33,396 - db.database - INFO - Database tables created successfully
2025-09-28 03:54:33,401 - db.database - INFO - Migration 001_add_users_table already applied
2025-09-28 03:54:33,402 - __main__ - INFO - Database initialized successfully
2025-09-28 03:54:33,402 - __main__ - INFO - Starting user authentication...
2025-09-28 03:54:48,808 - models.user - INFO - User authenticated successfully: user1
2025-09-28 03:54:48,822 - models.session - INFO - Session created for user user1
2025-09-28 03:54:48,842 - __main__ - INFO - User authenticated: user1
2025-09-28 03:54:48,852 - __main__ - INFO - Creating main application window...
2025-09-28 03:54:50,616 - __main__ - INFO - Application created successfully
2025-09-28 03:54:50,616 - __main__ - INFO - Starting main event loop...
2025-09-28 03:55:32,725 - __main__ - INFO - Application shutting down...
2025-09-28 03:55:32,776 - models.session - INFO - Session invalidated for token AJYEBLCa...
2025-09-28 03:55:32,778 - __main__ - INFO - User logged out
2025-09-28 03:58:25,583 - __main__ - INFO - Starting Document Management System v1.0.0
2025-09-28 03:58:25,583 - __main__ - INFO - Initializing database...
2025-09-28 03:58:25,584 - db.database - INFO - Database tables created successfully
2025-09-28 03:58:25,587 - db.database - INFO - Migration 001_add_users_table already applied
2025-09-28 03:58:25,587 - __main__ - INFO - Database initialized successfully
2025-09-28 03:58:25,587 - __main__ - INFO - Starting user authentication...
2025-09-28 03:58:39,356 - models.user - INFO - User authenticated successfully: user1
2025-09-28 03:58:39,369 - models.session - INFO - Session created for user user1
2025-09-28 03:58:39,381 - __main__ - INFO - User authenticated: user1
2025-09-28 03:58:39,424 - __main__ - INFO - Creating main application window...
2025-09-28 03:58:40,618 - __main__ - INFO - Application created successfully
2025-09-28 03:58:40,618 - __main__ - INFO - Starting main event loop...
2025-09-28 03:58:53,747 - models.document - INFO - Document added successfully: أمن المعلومات شرح المقرر كامل
2025-09-28 03:59:05,351 - models.document - INFO - Document added successfully: HubSpot - Learn to Code with ChatGPT ft. Sundas Khalid
2025-09-28 04:00:04,686 - __main__ - INFO - Application shutting down...
2025-09-28 04:00:04,709 - models.session - INFO - Session invalidated for token 9nLxppgb...
2025-09-28 04:00:04,710 - __main__ - INFO - User logged out
