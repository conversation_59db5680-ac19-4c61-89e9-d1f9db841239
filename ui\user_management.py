"""
User management and profile interfaces.
"""
import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional, List

from ui.themes import theme_manager
from models.session import session_manager
from models.user import user_manager
from db.models import User, UserRole

class UserProfileWindow:
    """User profile management window."""
    
    def __init__(self, parent, user: User):
        """Initialize user profile window."""
        self.user = user
        self.result = None
        
        # Create profile window
        self.window = tk.Toplevel(parent)
        self.window.title("User Profile")
        self.window.geometry("500x400")
        self.window.resizable(False, False)
        self.window.transient(parent)
        self.window.grab_set()
        
        # Center window
        self.window.update_idletasks()
        x = parent.winfo_x() + (parent.winfo_width() // 2) - 250
        y = parent.winfo_y() + (parent.winfo_height() // 2) - 200
        self.window.geometry(f"500x400+{x}+{y}")
        
        # Apply theme
        theme_manager.apply_theme(self.window)
        
        # Create UI
        self._create_widgets()
        
        # Wait for window to close
        self.window.wait_window()
    
    def _create_widgets(self):
        """Create profile window widgets."""
        # Main frame
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Title
        title_label = ttk.Label(
            main_frame,
            text="User Profile",
            font=('Arial', 16, 'bold')
        )
        title_label.pack(pady=(0, 20))
        
        # Profile form frame
        form_frame = ttk.Frame(main_frame, style='Card.TFrame')
        form_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Username (read-only)
        ttk.Label(form_frame, text="Username:").grid(row=0, column=0, sticky=tk.W, padx=20, pady=(20, 5))
        username_label = ttk.Label(form_frame, text=self.user.username, font=('Arial', 11, 'bold'))
        username_label.grid(row=0, column=1, sticky=tk.W, padx=(0, 20), pady=(20, 5))
        
        # Email
        ttk.Label(form_frame, text="Email:").grid(row=1, column=0, sticky=tk.W, padx=20, pady=(0, 5))
        self.email_var = tk.StringVar(value=self.user.email)
        self.email_entry = ttk.Entry(form_frame, textvariable=self.email_var, width=30)
        self.email_entry.grid(row=1, column=1, sticky=tk.W, padx=(0, 20), pady=(0, 15))
        
        # Full name
        ttk.Label(form_frame, text="Full Name:").grid(row=2, column=0, sticky=tk.W, padx=20, pady=(0, 5))
        self.full_name_var = tk.StringVar(value=self.user.full_name)
        self.full_name_entry = ttk.Entry(form_frame, textvariable=self.full_name_var, width=30)
        self.full_name_entry.grid(row=2, column=1, sticky=tk.W, padx=(0, 20), pady=(0, 15))
        
        # Role (read-only for non-admins)
        ttk.Label(form_frame, text="Role:").grid(row=3, column=0, sticky=tk.W, padx=20, pady=(0, 5))
        role_label = ttk.Label(form_frame, text=self.user.role, font=('Arial', 11))
        role_label.grid(row=3, column=1, sticky=tk.W, padx=(0, 20), pady=(0, 15))
        
        # Account status
        ttk.Label(form_frame, text="Status:").grid(row=4, column=0, sticky=tk.W, padx=20, pady=(0, 5))
        status_text = "Active" if self.user.is_active else "Inactive"
        status_label = ttk.Label(form_frame, text=status_text, font=('Arial', 11))
        status_label.grid(row=4, column=1, sticky=tk.W, padx=(0, 20), pady=(0, 15))
        
        # Last login
        ttk.Label(form_frame, text="Last Login:").grid(row=5, column=0, sticky=tk.W, padx=20, pady=(0, 5))
        last_login_text = self.user.last_login.strftime("%Y-%m-%d %H:%M") if self.user.last_login else "Never"
        last_login_label = ttk.Label(form_frame, text=last_login_text, font=('Arial', 11))
        last_login_label.grid(row=5, column=1, sticky=tk.W, padx=(0, 20), pady=(0, 20))
        
        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Change password button
        change_password_btn = ttk.Button(
            buttons_frame,
            text="Change Password",
            command=self._change_password,
            width=15
        )
        change_password_btn.pack(side=tk.LEFT)
        
        # Cancel button
        cancel_btn = ttk.Button(
            buttons_frame,
            text="Cancel",
            command=self._cancel,
            width=15
        )
        cancel_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        # Save button
        save_btn = ttk.Button(
            buttons_frame,
            text="Save Changes",
            style='Primary.TButton',
            command=self._save_changes,
            width=15
        )
        save_btn.pack(side=tk.RIGHT)
        
        # Status label
        self.status_var = tk.StringVar()
        self.status_label = ttk.Label(
            main_frame,
            textvariable=self.status_var,
            foreground='red',
            font=('Arial', 9)
        )
        self.status_label.pack()
    
    def _save_changes(self):
        """Save profile changes."""
        try:
            email = self.email_var.get().strip()
            full_name = self.full_name_var.get().strip()
            
            if not email or not full_name:
                self.status_var.set("Email and full name are required")
                return
            
            if '@' not in email:
                self.status_var.set("Please enter a valid email address")
                return
            
            # Update user
            success = user_manager.update_user(
                user_id=self.user.id,
                email=email,
                full_name=full_name
            )
            
            if success:
                self.result = True
                messagebox.showinfo("Success", "Profile updated successfully!")
                self.window.destroy()
            else:
                self.status_var.set("Failed to update profile")
                
        except ValueError as e:
            self.status_var.set(str(e))
        except Exception as e:
            self.status_var.set(f"Error updating profile: {e}")
    
    def _change_password(self):
        """Show change password dialog."""
        password_window = ChangePasswordWindow(self.window, self.user)
        if password_window.result:
            messagebox.showinfo("Success", "Password changed successfully!")
    
    def _cancel(self):
        """Cancel profile editing."""
        self.window.destroy()


class ChangePasswordWindow:
    """Change password dialog."""
    
    def __init__(self, parent, user: User):
        """Initialize change password window."""
        self.user = user
        self.result = None
        
        # Create password window
        self.window = tk.Toplevel(parent)
        self.window.title("Change Password")
        self.window.geometry("400x300")
        self.window.resizable(False, False)
        self.window.transient(parent)
        self.window.grab_set()
        
        # Center window
        self.window.update_idletasks()
        x = parent.winfo_x() + (parent.winfo_width() // 2) - 200
        y = parent.winfo_y() + (parent.winfo_height() // 2) - 150
        self.window.geometry(f"400x300+{x}+{y}")
        
        # Apply theme
        theme_manager.apply_theme(self.window)
        
        # Create UI
        self._create_widgets()
        
        # Focus on current password field
        self.current_password_entry.focus()
        
        # Wait for window to close
        self.window.wait_window()
    
    def _create_widgets(self):
        """Create change password widgets."""
        # Main frame
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Title
        title_label = ttk.Label(
            main_frame,
            text="Change Password",
            font=('Arial', 14, 'bold')
        )
        title_label.pack(pady=(0, 20))
        
        # Password form frame
        form_frame = ttk.Frame(main_frame, style='Card.TFrame')
        form_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Current password
        ttk.Label(form_frame, text="Current Password:").pack(anchor=tk.W, padx=20, pady=(20, 5))
        self.current_password_var = tk.StringVar()
        self.current_password_entry = ttk.Entry(
            form_frame,
            textvariable=self.current_password_var,
            show="*",
            width=30
        )
        self.current_password_entry.pack(padx=20, pady=(0, 15))
        
        # New password
        ttk.Label(form_frame, text="New Password:").pack(anchor=tk.W, padx=20, pady=(0, 5))
        self.new_password_var = tk.StringVar()
        self.new_password_entry = ttk.Entry(
            form_frame,
            textvariable=self.new_password_var,
            show="*",
            width=30
        )
        self.new_password_entry.pack(padx=20, pady=(0, 15))
        
        # Confirm new password
        ttk.Label(form_frame, text="Confirm New Password:").pack(anchor=tk.W, padx=20, pady=(0, 5))
        self.confirm_password_var = tk.StringVar()
        self.confirm_password_entry = ttk.Entry(
            form_frame,
            textvariable=self.confirm_password_var,
            show="*",
            width=30
        )
        self.confirm_password_entry.pack(padx=20, pady=(0, 20))
        
        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Cancel button
        cancel_btn = ttk.Button(
            buttons_frame,
            text="Cancel",
            command=self._cancel,
            width=15
        )
        cancel_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        # Change password button
        change_btn = ttk.Button(
            buttons_frame,
            text="Change Password",
            style='Primary.TButton',
            command=self._change_password,
            width=15
        )
        change_btn.pack(side=tk.RIGHT)
        
        # Status label
        self.status_var = tk.StringVar()
        self.status_label = ttk.Label(
            main_frame,
            textvariable=self.status_var,
            foreground='red',
            font=('Arial', 9)
        )
        self.status_label.pack()
    
    def _change_password(self):
        """Change user password."""
        try:
            current_password = self.current_password_var.get()
            new_password = self.new_password_var.get()
            confirm_password = self.confirm_password_var.get()
            
            if not all([current_password, new_password, confirm_password]):
                self.status_var.set("All fields are required")
                return
            
            if new_password != confirm_password:
                self.status_var.set("New passwords do not match")
                return
            
            if len(new_password) < 6:
                self.status_var.set("New password must be at least 6 characters")
                return
            
            # Change password
            success = user_manager.change_password(
                user_id=self.user.id,
                old_password=current_password,
                new_password=new_password
            )
            
            if success:
                self.result = True
                self.window.destroy()
            else:
                self.status_var.set("Current password is incorrect")
                
        except Exception as e:
            self.status_var.set(f"Error changing password: {e}")
    
    def _cancel(self):
        """Cancel password change."""
        self.window.destroy()
