"""
UI themes and styling for the Document Management System.
"""
import tkinter as tk
from tkinter import ttk

class ThemeManager:
    """Manages UI themes and styling."""
    
    def __init__(self):
        """Initialize theme manager."""
        self.current_theme = "clam"
        self.colors = {
            'primary': '#2196F3',
            'primary_dark': '#1976D2',
            'secondary': '#FFC107',
            'success': '#4CAF50',
            'danger': '#F44336',
            'warning': '#FF9800',
            'info': '#00BCD4',
            'light': '#F5F5F5',
            'dark': '#212121',
            'white': '#FFFFFF',
            'gray': '#9E9E9E'
        }
    
    def apply_theme(self, root: tk.Tk):
        """Apply the current theme to the root window."""
        style = ttk.Style(root)
        
        # Set the theme
        style.theme_use(self.current_theme)
        
        # Configure custom styles
        self._configure_button_styles(style)
        self._configure_frame_styles(style)
        self._configure_treeview_styles(style)
        self._configure_entry_styles(style)
    
    def _configure_button_styles(self, style: ttk.Style):
        """Configure button styles."""
        # Primary button
        style.configure(
            'Primary.TButton',
            background=self.colors['primary'],
            foreground=self.colors['white'],
            borderwidth=0,
            focuscolor='none'
        )
        style.map(
            'Primary.TButton',
            background=[('active', self.colors['primary_dark'])]
        )
        
        # Success button
        style.configure(
            'Success.TButton',
            background=self.colors['success'],
            foreground=self.colors['white'],
            borderwidth=0,
            focuscolor='none'
        )
        
        # Danger button
        style.configure(
            'Danger.TButton',
            background=self.colors['danger'],
            foreground=self.colors['white'],
            borderwidth=0,
            focuscolor='none'
        )

        # Secondary button
        style.configure(
            'Secondary.TButton',
            background=self.colors['gray'],
            foreground=self.colors['white'],
            borderwidth=0,
            focuscolor='none'
        )
        style.map(
            'Secondary.TButton',
            background=[('active', self.colors['dark'])]
        )

        # Warning button
        style.configure(
            'Warning.TButton',
            background=self.colors['warning'],
            foreground=self.colors['white'],
            borderwidth=0,
            focuscolor='none'
        )
    
    def _configure_frame_styles(self, style: ttk.Style):
        """Configure frame styles."""
        style.configure(
            'Card.TFrame',
            background=self.colors['white'],
            relief='solid',
            borderwidth=1
        )
        
        style.configure(
            'Toolbar.TFrame',
            background=self.colors['light'],
            relief='flat'
        )
    
    def _configure_treeview_styles(self, style: ttk.Style):
        """Configure treeview styles."""
        style.configure(
            'Documents.Treeview',
            background=self.colors['white'],
            foreground=self.colors['dark'],
            rowheight=25,
            fieldbackground=self.colors['white']
        )
        
        style.configure(
            'Documents.Treeview.Heading',
            background=self.colors['light'],
            foreground=self.colors['dark'],
            relief='flat'
        )
        
        style.map(
            'Documents.Treeview',
            background=[('selected', self.colors['primary'])],
            foreground=[('selected', self.colors['white'])]
        )
    
    def _configure_entry_styles(self, style: ttk.Style):
        """Configure entry styles."""
        style.configure(
            'Search.TEntry',
            fieldbackground=self.colors['white'],
            borderwidth=1,
            relief='solid'
        )

# Global theme manager instance
theme_manager = ThemeManager()
