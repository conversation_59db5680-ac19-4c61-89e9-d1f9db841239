"""
Login and registration windows for user authentication.
"""
import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional, Callable

from config.settings import APP_NAME
from ui.themes import theme_manager
from models.session import session_manager
from models.user import user_manager
from db.models import UserRole

class LoginWindow:
    """Login window for user authentication."""
    
    def __init__(self, on_login_success: Callable = None):
        """Initialize login window."""
        self.on_login_success = on_login_success
        self.result = None
        
        # Create login window
        self.window = tk.Tk()
        self.window.title(f"{APP_NAME} - Login")
        self.window.geometry("400x500")
        self.window.resizable(False, False)
        
        # Center window
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - 200
        y = (self.window.winfo_screenheight() // 2) - 250
        self.window.geometry(f"400x500+{x}+{y}")
        
        # Apply theme
        theme_manager.apply_theme(self.window)
        
        # Create UI
        self._create_widgets()
        
        # Handle window close
        self.window.protocol("WM_DELETE_WINDOW", self._on_close)
        
        # Focus on username field
        self.username_entry.focus()
    
    def _create_widgets(self):
        """Create login window widgets."""
        # Main frame
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)
        
        # Title
        title_label = ttk.Label(
            main_frame, 
            text=APP_NAME,
            font=('Arial', 18, 'bold')
        )
        title_label.pack(pady=(0, 10))
        
        subtitle_label = ttk.Label(
            main_frame,
            text="Please sign in to continue",
            font=('Arial', 10)
        )
        subtitle_label.pack(pady=(0, 30))
        
        # Login form frame
        form_frame = ttk.Frame(main_frame, style='Card.TFrame')
        form_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Username field
        ttk.Label(form_frame, text="Username:").pack(anchor=tk.W, padx=20, pady=(20, 5))
        self.username_var = tk.StringVar()
        self.username_entry = ttk.Entry(
            form_frame,
            textvariable=self.username_var,
            font=('Arial', 11),
            width=30
        )
        self.username_entry.pack(padx=20, pady=(0, 15))
        self.username_entry.bind('<Return>', lambda e: self.password_entry.focus())
        
        # Password field
        ttk.Label(form_frame, text="Password:").pack(anchor=tk.W, padx=20, pady=(0, 5))
        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(
            form_frame,
            textvariable=self.password_var,
            show="*",
            font=('Arial', 11),
            width=30
        )
        self.password_entry.pack(padx=20, pady=(0, 20))
        self.password_entry.bind('<Return>', lambda e: self._login())
        
        # Login button
        self.login_btn = ttk.Button(
            main_frame,
            text="Sign In",
            style='Primary.TButton',
            command=self._login,
            width=25
        )
        self.login_btn.pack(pady=(0, 15))
        
        # Register button
        register_btn = ttk.Button(
            main_frame,
            text="Create New Account",
            command=self._show_register,
            width=25
        )
        register_btn.pack(pady=(0, 20))
        
        # Status label
        self.status_var = tk.StringVar()
        self.status_label = ttk.Label(
            main_frame,
            textvariable=self.status_var,
            foreground='red',
            font=('Arial', 9)
        )
        self.status_label.pack()
        
        # Default admin info (if no users exist)
        self._check_and_show_default_admin_info()
    
    def _check_and_show_default_admin_info(self):
        """Show default admin info if no users exist."""
        try:
            users = user_manager.get_all_users(include_inactive=True)
            if not users:
                # Create default admin
                session_manager.create_default_admin()
                
                # Show info
                info_frame = ttk.Frame(self.window, style='Card.TFrame')
                info_frame.pack(fill=tk.X, padx=30, pady=(0, 20))
                
                ttk.Label(
                    info_frame,
                    text="First Time Setup",
                    font=('Arial', 12, 'bold')
                ).pack(pady=(15, 5))
                
                ttk.Label(
                    info_frame,
                    text="Default admin account created:",
                    font=('Arial', 9)
                ).pack()
                
                ttk.Label(
                    info_frame,
                    text="Username: admin",
                    font=('Arial', 9, 'bold')
                ).pack()
                
                ttk.Label(
                    info_frame,
                    text="Password: admin123",
                    font=('Arial', 9, 'bold')
                ).pack()
                
                ttk.Label(
                    info_frame,
                    text="Please change the password after login!",
                    font=('Arial', 8),
                    foreground='red'
                ).pack(pady=(5, 15))
                
        except Exception as e:
            print(f"Error checking default admin: {e}")
    
    def _login(self):
        """Handle login attempt."""
        username = self.username_var.get().strip()
        password = self.password_var.get()
        
        if not username or not password:
            self.status_var.set("Please enter both username and password")
            return
        
        # Disable login button during authentication
        self.login_btn.config(state=tk.DISABLED)
        self.status_var.set("Authenticating...")
        self.window.update()
        
        try:
            # Attempt login
            login_result = session_manager.login(username, password)
            
            if login_result:
                self.result = login_result
                self.status_var.set("Login successful!")
                
                # Call success callback
                if self.on_login_success:
                    self.on_login_success(login_result)
                
                self.window.destroy()
            else:
                self.status_var.set("Invalid username or password")
                self.login_btn.config(state=tk.NORMAL)
                
        except Exception as e:
            self.status_var.set(f"Login error: {e}")
            self.login_btn.config(state=tk.NORMAL)
    
    def _show_register(self):
        """Show registration window."""
        register_window = RegistrationWindow(self.window)
        if register_window.result:
            messagebox.showinfo(
                "Registration Successful",
                f"Account created successfully for {register_window.result['username']}!\n"
                "You can now log in with your credentials."
            )
    
    def _on_close(self):
        """Handle window close."""
        self.window.quit()
        self.window.destroy()
    
    def show(self):
        """Show the login window."""
        self.window.mainloop()
        return self.result


class RegistrationWindow:
    """Registration window for creating new user accounts."""
    
    def __init__(self, parent=None):
        """Initialize registration window."""
        self.result = None
        
        # Create registration window
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.window.title(f"{APP_NAME} - Create Account")
        self.window.geometry("450x600")
        self.window.resizable(False, False)
        
        if parent:
            self.window.transient(parent)
            self.window.grab_set()
        
        # Center window
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - 225
        y = (self.window.winfo_screenheight() // 2) - 300
        self.window.geometry(f"450x600+{x}+{y}")
        
        # Apply theme
        theme_manager.apply_theme(self.window)
        
        # Create UI
        self._create_widgets()
        
        # Focus on username field
        self.username_entry.focus()
        
        # Wait for window to close
        self.window.wait_window()
    
    def _create_widgets(self):
        """Create registration window widgets."""
        # Main frame
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)
        
        # Title
        title_label = ttk.Label(
            main_frame,
            text="Create New Account",
            font=('Arial', 16, 'bold')
        )
        title_label.pack(pady=(0, 20))
        
        # Registration form frame
        form_frame = ttk.Frame(main_frame, style='Card.TFrame')
        form_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Username field
        ttk.Label(form_frame, text="Username:").pack(anchor=tk.W, padx=20, pady=(20, 5))
        self.username_var = tk.StringVar()
        self.username_entry = ttk.Entry(
            form_frame,
            textvariable=self.username_var,
            font=('Arial', 11),
            width=35
        )
        self.username_entry.pack(padx=20, pady=(0, 15))
        
        # Email field
        ttk.Label(form_frame, text="Email:").pack(anchor=tk.W, padx=20, pady=(0, 5))
        self.email_var = tk.StringVar()
        self.email_entry = ttk.Entry(
            form_frame,
            textvariable=self.email_var,
            font=('Arial', 11),
            width=35
        )
        self.email_entry.pack(padx=20, pady=(0, 15))
        
        # Full name field
        ttk.Label(form_frame, text="Full Name:").pack(anchor=tk.W, padx=20, pady=(0, 5))
        self.full_name_var = tk.StringVar()
        self.full_name_entry = ttk.Entry(
            form_frame,
            textvariable=self.full_name_var,
            font=('Arial', 11),
            width=35
        )
        self.full_name_entry.pack(padx=20, pady=(0, 15))
        
        # Role field (only for admin users)
        ttk.Label(form_frame, text="Role:").pack(anchor=tk.W, padx=20, pady=(0, 5))
        self.role_var = tk.StringVar(value=UserRole.EMPLOYEE.value)
        self.role_combo = ttk.Combobox(
            form_frame,
            textvariable=self.role_var,
            values=UserRole.get_all_roles(),
            state="readonly",
            width=32
        )
        self.role_combo.pack(padx=20, pady=(0, 15))
        
        # Password field
        ttk.Label(form_frame, text="Password:").pack(anchor=tk.W, padx=20, pady=(0, 5))
        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(
            form_frame,
            textvariable=self.password_var,
            show="*",
            font=('Arial', 11),
            width=35
        )
        self.password_entry.pack(padx=20, pady=(0, 15))
        
        # Confirm password field
        ttk.Label(form_frame, text="Confirm Password:").pack(anchor=tk.W, padx=20, pady=(0, 5))
        self.confirm_password_var = tk.StringVar()
        self.confirm_password_entry = ttk.Entry(
            form_frame,
            textvariable=self.confirm_password_var,
            show="*",
            font=('Arial', 11),
            width=35
        )
        self.confirm_password_entry.pack(padx=20, pady=(0, 20))
        
        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Cancel button
        cancel_btn = ttk.Button(
            buttons_frame,
            text="Cancel",
            command=self._cancel,
            width=15
        )
        cancel_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        # Create account button
        create_btn = ttk.Button(
            buttons_frame,
            text="Create Account",
            style='Primary.TButton',
            command=self._create_account,
            width=15
        )
        create_btn.pack(side=tk.RIGHT)
        
        # Status label
        self.status_var = tk.StringVar()
        self.status_label = ttk.Label(
            main_frame,
            textvariable=self.status_var,
            foreground='red',
            font=('Arial', 9)
        )
        self.status_label.pack()
    
    def _create_account(self):
        """Handle account creation."""
        # Get form data
        username = self.username_var.get().strip()
        email = self.email_var.get().strip()
        full_name = self.full_name_var.get().strip()
        role = self.role_var.get()
        password = self.password_var.get()
        confirm_password = self.confirm_password_var.get()
        
        # Validate input
        if not all([username, email, full_name, password, confirm_password]):
            self.status_var.set("All fields are required")
            return
        
        if password != confirm_password:
            self.status_var.set("Passwords do not match")
            return
        
        if len(password) < 6:
            self.status_var.set("Password must be at least 6 characters")
            return
        
        if '@' not in email:
            self.status_var.set("Please enter a valid email address")
            return
        
        try:
            # Create user
            user = user_manager.create_user(
                username=username,
                email=email,
                password=password,
                full_name=full_name,
                role=role
            )
            
            if user:
                self.result = {
                    'username': username,
                    'email': email,
                    'full_name': full_name,
                    'role': role
                }
                self.window.destroy()
            else:
                self.status_var.set("Failed to create account")
                
        except ValueError as e:
            self.status_var.set(str(e))
        except Exception as e:
            self.status_var.set(f"Error creating account: {e}")
    
    def _cancel(self):
        """Handle cancel button."""
        self.window.destroy()
