"""
User management and authentication functionality.
"""
import logging
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any

try:
    import bcrypt
    BCRYPT_AVAILABLE = True
except ImportError:
    import hashlib
    import secrets
    BCRYPT_AVAILABLE = False

from db.database import db_manager
from db.models import User, UserSession, AuditLog, UserRole

logger = logging.getLogger(__name__)

def hash_password(password: str) -> str:
    """Hash password using bcrypt or fallback to SHA-256."""
    if BCRYPT_AVAILABLE:
        # Use bcrypt for secure password hashing
        salt = bcrypt.gensalt()
        password_hash = bcrypt.hashpw(password.encode('utf-8'), salt)
        return password_hash.decode('utf-8')
    else:
        # Fallback to SHA-256 with salt (for development without bcrypt)
        salt = secrets.token_hex(16)
        password_hash = hashlib.sha256((password + salt).encode()).hexdigest()
        return f"sha256:{salt}:{password_hash}"

def verify_password(password: str, password_hash: str) -> bool:
    """Verify password against hash."""
    if BCRYPT_AVAILABLE and not password_hash.startswith('sha256:'):
        # Use bcrypt verification
        try:
            return bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8'))
        except ValueError:
            return False
    else:
        # Fallback SHA-256 verification
        try:
            if password_hash.startswith('sha256:'):
                _, salt, stored_hash = password_hash.split(':')
            else:
                # Legacy format
                salt, stored_hash = password_hash.split(':')
            password_hash_check = hashlib.sha256((password + salt).encode()).hexdigest()
            return password_hash_check == stored_hash
        except ValueError:
            return False

class UserManager:
    """Manages user operations including authentication and authorization."""
    
    def __init__(self):
        """Initialize user manager."""
        self.session_timeout_hours = 24  # 24 hours session timeout
    
    def create_user(self, username: str, email: str, password: str, 
                   full_name: str, role: str = UserRole.EMPLOYEE.value) -> Optional[User]:
        """Create a new user."""
        try:
            # Validate input
            if not username or not email or not password or not full_name:
                raise ValueError("All fields are required")
            
            if role not in UserRole.get_all_roles():
                raise ValueError(f"Invalid role: {role}")
            
            # Check if username or email already exists
            existing_user = self.get_user_by_username(username)
            if existing_user:
                raise ValueError("Username already exists")
            
            existing_user = self.get_user_by_email(email)
            if existing_user:
                raise ValueError("Email already exists")
            
            # Hash password
            password_hash = hash_password(password)
            
            # Create user record
            user = User(
                username=username,
                email=email,
                password_hash=password_hash,
                full_name=full_name,
                role=role,
                is_active=True,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            # Insert into database
            query = '''
                INSERT INTO users (username, email, password_hash, full_name, 
                                 role, is_active, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            '''
            params = (
                user.username, user.email, user.password_hash, user.full_name,
                user.role, user.is_active, user.created_at, user.updated_at
            )
            
            user.id = db_manager.execute_insert(query, params)
            
            # Log user creation
            self.log_audit_event(
                user_id=None,  # System action
                action="USER_CREATED",
                resource_type="user",
                resource_id=user.id,
                details=f"User created: {username} ({role})"
            )
            
            logger.info(f"User created successfully: {username}")
            return user
            
        except Exception as e:
            logger.error(f"Error creating user: {e}")
            raise
    
    def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """Authenticate user with username and password."""
        try:
            user = self.get_user_by_username(username)
            if not user:
                logger.warning(f"Authentication failed: user not found - {username}")
                return None
            
            if not user.is_active:
                logger.warning(f"Authentication failed: user inactive - {username}")
                return None
            
            if not verify_password(password, user.password_hash):
                logger.warning(f"Authentication failed: invalid password - {username}")
                self.log_audit_event(
                    user_id=user.id,
                    action="LOGIN_FAILED",
                    details="Invalid password"
                )
                return None
            
            # Update last login
            self.update_last_login(user.id)
            
            # Log successful login
            self.log_audit_event(
                user_id=user.id,
                action="LOGIN_SUCCESS",
                details="User logged in successfully"
            )
            
            logger.info(f"User authenticated successfully: {username}")
            return user
            
        except Exception as e:
            logger.error(f"Error authenticating user: {e}")
            return None
    
    def get_user_by_id(self, user_id: int) -> Optional[User]:
        """Get user by ID."""
        try:
            query = "SELECT * FROM users WHERE id = ?"
            results = db_manager.execute_query(query, (user_id,))
            
            if results:
                return User.from_dict(results[0])
            return None
            
        except Exception as e:
            logger.error(f"Error getting user by ID {user_id}: {e}")
            return None
    
    def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username."""
        try:
            query = "SELECT * FROM users WHERE username = ?"
            results = db_manager.execute_query(query, (username,))
            
            if results:
                return User.from_dict(results[0])
            return None
            
        except Exception as e:
            logger.error(f"Error getting user by username {username}: {e}")
            return None
    
    def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email."""
        try:
            query = "SELECT * FROM users WHERE email = ?"
            results = db_manager.execute_query(query, (email,))
            
            if results:
                return User.from_dict(results[0])
            return None
            
        except Exception as e:
            logger.error(f"Error getting user by email {email}: {e}")
            return None
    
    def get_all_users(self, include_inactive: bool = False) -> List[User]:
        """Get all users."""
        try:
            if include_inactive:
                query = "SELECT * FROM users ORDER BY created_at DESC"
                params = ()
            else:
                query = "SELECT * FROM users WHERE is_active = 1 ORDER BY created_at DESC"
                params = ()
            
            results = db_manager.execute_query(query, params)
            return [User.from_dict(row) for row in results]
            
        except Exception as e:
            logger.error(f"Error getting users: {e}")
            return []
    
    def update_user(self, user_id: int, **kwargs) -> bool:
        """Update user information."""
        try:
            # Build update query dynamically
            allowed_fields = ['email', 'full_name', 'role', 'is_active']
            updates = []
            params = []
            
            for field, value in kwargs.items():
                if field in allowed_fields:
                    updates.append(f"{field} = ?")
                    params.append(value)
            
            if not updates:
                return False
            
            # Add updated_at
            updates.append("updated_at = ?")
            params.append(datetime.now())
            params.append(user_id)
            
            query = f"UPDATE users SET {', '.join(updates)} WHERE id = ?"
            rows_affected = db_manager.execute_update(query, tuple(params))
            
            if rows_affected > 0:
                self.log_audit_event(
                    user_id=user_id,
                    action="USER_UPDATED",
                    resource_type="user",
                    resource_id=user_id,
                    details=f"User updated: {kwargs}"
                )
                logger.info(f"User {user_id} updated successfully")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error updating user {user_id}: {e}")
            return False
    
    def change_password(self, user_id: int, old_password: str, new_password: str) -> bool:
        """Change user password."""
        try:
            user = self.get_user_by_id(user_id)
            if not user:
                return False
            
            # Verify old password
            if not verify_password(old_password, user.password_hash):
                logger.warning(f"Password change failed: invalid old password - user {user_id}")
                return False
            
            # Hash new password
            new_password_hash = hash_password(new_password)
            
            # Update password
            query = "UPDATE users SET password_hash = ?, updated_at = ? WHERE id = ?"
            rows_affected = db_manager.execute_update(
                query, 
                (new_password_hash, datetime.now(), user_id)
            )
            
            if rows_affected > 0:
                self.log_audit_event(
                    user_id=user_id,
                    action="PASSWORD_CHANGED",
                    details="User changed password"
                )
                logger.info(f"Password changed successfully for user {user_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error changing password for user {user_id}: {e}")
            return False
    
    def update_last_login(self, user_id: int):
        """Update user's last login timestamp."""
        try:
            query = "UPDATE users SET last_login = ? WHERE id = ?"
            db_manager.execute_update(query, (datetime.now(), user_id))
        except Exception as e:
            logger.error(f"Error updating last login for user {user_id}: {e}")
    
    def log_audit_event(self, user_id: Optional[int], action: str, 
                       resource_type: str = "", resource_id: Optional[int] = None,
                       details: str = "", ip_address: str = "", user_agent: str = ""):
        """Log audit event."""
        try:
            query = '''
                INSERT INTO audit_logs (user_id, action, resource_type, resource_id, 
                                      details, ip_address, user_agent, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            '''
            params = (
                user_id, action, resource_type, resource_id,
                details, ip_address, user_agent, datetime.now()
            )
            
            db_manager.execute_insert(query, params)
            
        except Exception as e:
            logger.error(f"Error logging audit event: {e}")

# Global user manager instance
user_manager = UserManager()
