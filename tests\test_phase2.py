#!/usr/bin/env python3
"""
Test script for Phase 2 (User Management & Authentication) functionality.
Tests user authentication, session management, and permissions.
"""

import sys
import os
import tempfile
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from db.database import db_manager
from models.user import user_manager, hash_password, verify_password
from models.session import session_manager
from models.document import document_manager
from db.models import User, UserR<PERSON>, UserSession

def test_password_hashing():
    """Test password hashing and verification."""
    print("Testing password hashing...")
    try:
        password = "test_password_123"
        
        # Test password hashing
        password_hash = hash_password(password)
        assert password_hash is not None, "Password hash is None"
        assert password_hash != password, "Password hash should not equal plain password"
        print("✅ Password hashing successful")
        
        # Test password verification
        assert verify_password(password, password_hash), "Password verification failed"
        assert not verify_password("wrong_password", password_hash), "Wrong password should not verify"
        print("✅ Password verification successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Password hashing test failed: {e}")
        return False

def test_user_creation():
    """Test user creation and management."""
    print("Testing user creation...")
    try:
        # Test creating a user
        import random
        username = f"testuser_{random.randint(1000, 9999)}"
        email = f"test_{random.randint(1000, 9999)}@example.com"

        user = user_manager.create_user(
            username=username,
            email=email,
            password="password123",
            full_name="Test User",
            role=UserRole.EMPLOYEE.value
        )
        
        assert user is not None, "User creation failed"
        assert user.id is not None, "User ID not assigned"
        assert user.username == username, "Username mismatch"
        assert user.email == email, "Email mismatch"
        assert user.role == UserRole.EMPLOYEE.value, "Role mismatch"
        print(f"✅ User created successfully (ID: {user.id})")
        
        # Test duplicate username
        try:
            duplicate_user = user_manager.create_user(
                username=username,  # Same username as above
                email="<EMAIL>",
                password="password123",
                full_name="Test User 2"
            )
            assert False, "Duplicate username should not be allowed"
        except ValueError:
            print("✅ Duplicate username prevention successful")
        
        # Test user retrieval
        retrieved_user = user_manager.get_user_by_id(user.id)
        assert retrieved_user is not None, "User retrieval failed"
        assert retrieved_user.username == user.username, "Retrieved user mismatch"
        print("✅ User retrieval successful")
        
        # Test user by username
        user_by_username = user_manager.get_user_by_username(username)
        assert user_by_username is not None, "User by username retrieval failed"
        assert user_by_username.id == user.id, "User by username mismatch"
        print("✅ User by username retrieval successful")
        
        # Test user update
        success = user_manager.update_user(user.id, full_name="Updated Test User")
        assert success, "User update failed"
        
        updated_user = user_manager.get_user_by_id(user.id)
        assert updated_user.full_name == "Updated Test User", "User update not reflected"
        print("✅ User update successful")
        
        return True
        
    except Exception as e:
        print(f"❌ User creation test failed: {e}")
        return False

def test_user_authentication():
    """Test user authentication."""
    print("Testing user authentication...")
    try:
        # Create test user
        import random
        username = f"authtest_{random.randint(1000, 9999)}"
        email = f"auth_{random.randint(1000, 9999)}@example.com"

        user = user_manager.create_user(
            username=username,
            email=email,
            password="authpass123",
            full_name="Auth Test User"
        )
        
        assert user is not None, "Test user creation failed"
        
        # Test successful authentication
        auth_user = user_manager.authenticate_user(username, "authpass123")
        assert auth_user is not None, "Authentication failed"
        assert auth_user.id == user.id, "Authenticated user mismatch"
        print("✅ User authentication successful")

        # Test failed authentication (wrong password)
        failed_auth = user_manager.authenticate_user(username, "wrongpassword")
        assert failed_auth is None, "Authentication should fail with wrong password"
        print("✅ Failed authentication (wrong password) handled correctly")
        
        # Test failed authentication (non-existent user)
        failed_auth = user_manager.authenticate_user("nonexistent", "password")
        assert failed_auth is None, "Authentication should fail for non-existent user"
        print("✅ Failed authentication (non-existent user) handled correctly")
        
        # Test password change
        success = user_manager.change_password(user.id, "authpass123", "newpassword123")
        assert success, "Password change failed"
        
        # Test authentication with new password
        auth_user = user_manager.authenticate_user(username, "newpassword123")
        assert auth_user is not None, "Authentication with new password failed"
        print("✅ Password change successful")
        
        return True
        
    except Exception as e:
        print(f"❌ User authentication test failed: {e}")
        return False

def test_session_management():
    """Test session management."""
    print("Testing session management...")
    try:
        # Create test user
        import random
        username = f"sessiontest_{random.randint(1000, 9999)}"
        email = f"session_{random.randint(1000, 9999)}@example.com"

        user = user_manager.create_user(
            username=username,
            email=email,
            password="sessionpass123",
            full_name="Session Test User"
        )
        
        assert user is not None, "Test user creation failed"
        
        # Test login (creates session)
        login_result = session_manager.login(username, "sessionpass123")
        assert login_result is not None, "Login failed"
        assert 'user' in login_result, "Login result missing user"
        assert 'session' in login_result, "Login result missing session"
        assert 'session_token' in login_result, "Login result missing session token"
        print("✅ Login and session creation successful")
        
        session_token = login_result['session_token']
        
        # Test session validation
        validated_user = session_manager.validate_session(session_token)
        assert validated_user is not None, "Session validation failed"
        assert validated_user.id == user.id, "Session validation user mismatch"
        print("✅ Session validation successful")
        
        # Test current user
        current_user = session_manager.get_current_user()
        assert current_user is not None, "Current user is None"
        assert current_user.id == user.id, "Current user mismatch"
        print("✅ Current user retrieval successful")
        
        # Test authentication check
        assert session_manager.is_authenticated(), "Authentication check failed"
        print("✅ Authentication check successful")
        
        # Test logout
        logout_success = session_manager.logout(session_token)
        assert logout_success, "Logout failed"
        
        # Test session validation after logout
        validated_user = session_manager.validate_session(session_token)
        assert validated_user is None, "Session should be invalid after logout"
        print("✅ Logout and session invalidation successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Session management test failed: {e}")
        return False

def test_role_permissions():
    """Test role-based permissions."""
    print("Testing role permissions...")
    try:
        # Test role permission checking
        assert UserRole.has_permission(UserRole.ADMIN.value, "read"), "Admin should have read permission"
        assert UserRole.has_permission(UserRole.ADMIN.value, "write"), "Admin should have write permission"
        assert UserRole.has_permission(UserRole.ADMIN.value, "delete"), "Admin should have delete permission"
        assert UserRole.has_permission(UserRole.ADMIN.value, "user_management"), "Admin should have user_management permission"
        
        assert UserRole.has_permission(UserRole.EMPLOYEE.value, "read"), "Employee should have read permission"
        assert UserRole.has_permission(UserRole.EMPLOYEE.value, "write"), "Employee should have write permission"
        assert UserRole.has_permission(UserRole.EMPLOYEE.value, "delete"), "Employee should have delete permission"
        assert not UserRole.has_permission(UserRole.EMPLOYEE.value, "user_management"), "Employee should not have user_management permission"
        
        assert UserRole.has_permission(UserRole.VIEWER.value, "read"), "Viewer should have read permission"
        assert not UserRole.has_permission(UserRole.VIEWER.value, "write"), "Viewer should not have write permission"
        assert not UserRole.has_permission(UserRole.VIEWER.value, "delete"), "Viewer should not have delete permission"
        assert not UserRole.has_permission(UserRole.VIEWER.value, "user_management"), "Viewer should not have user_management permission"
        
        print("✅ Role permission checking successful")
        
        # Test user permission methods
        admin_user = User(username="admin", role=UserRole.ADMIN.value)
        employee_user = User(username="employee", role=UserRole.EMPLOYEE.value)
        viewer_user = User(username="viewer", role=UserRole.VIEWER.value)
        
        assert admin_user.is_admin(), "Admin user should be admin"
        assert admin_user.can_manage_users(), "Admin should be able to manage users"
        assert admin_user.can_delete_documents(), "Admin should be able to delete documents"
        assert admin_user.can_write_documents(), "Admin should be able to write documents"
        
        assert not employee_user.is_admin(), "Employee user should not be admin"
        assert not employee_user.can_manage_users(), "Employee should not be able to manage users"
        assert employee_user.can_delete_documents(), "Employee should be able to delete documents"
        assert employee_user.can_write_documents(), "Employee should be able to write documents"
        
        assert not viewer_user.is_admin(), "Viewer user should not be admin"
        assert not viewer_user.can_manage_users(), "Viewer should not be able to manage users"
        assert not viewer_user.can_delete_documents(), "Viewer should not be able to delete documents"
        assert not viewer_user.can_write_documents(), "Viewer should not be able to write documents"
        
        print("✅ User permission methods successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Role permissions test failed: {e}")
        return False

def test_document_user_association():
    """Test document-user association."""
    print("Testing document-user association...")
    try:
        # Create test user
        import random
        username = f"doctest_{random.randint(1000, 9999)}"
        email = f"doc_{random.randint(1000, 9999)}@example.com"

        user = user_manager.create_user(
            username=username,
            email=email,
            password="docpass123",
            full_name="Doc Test User"
        )
        
        assert user is not None, "Test user creation failed"
        
        # Create test file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("Test document content for user association")
            test_file = f.name
        
        # Add document with user association
        document = document_manager.add_document(
            file_path=test_file,
            title="User Associated Document",
            description="Test document with user association",
            category="Test",
            user_id=user.id
        )
        
        assert document is not None, "Document creation failed"
        assert document.user_id == user.id, "Document user association failed"
        print("✅ Document-user association successful")
        
        # Clean up
        document_manager.delete_document(document.id)
        os.unlink(test_file)
        
        return True
        
    except Exception as e:
        print(f"❌ Document-user association test failed: {e}")
        return False

def test_default_admin_creation():
    """Test default admin creation."""
    print("Testing default admin creation...")
    try:
        # Clear all users first (for testing)
        # Note: This is only for testing - don't do this in production
        
        # Create default admin
        admin_user = session_manager.create_default_admin()
        
        if admin_user:
            assert admin_user.username == "admin", "Default admin username incorrect"
            assert admin_user.role == UserRole.ADMIN.value, "Default admin role incorrect"
            assert admin_user.is_admin(), "Default admin should be admin"
            print("✅ Default admin creation successful")
        else:
            # Admin already exists, which is also valid
            print("✅ Default admin already exists (expected behavior)")
        
        return True
        
    except Exception as e:
        print(f"❌ Default admin creation test failed: {e}")
        return False

def run_all_tests():
    """Run all Phase 2 tests."""
    print("=" * 60)
    print("Running Phase 2 (User Management & Authentication) Tests")
    print("=" * 60)
    
    # Initialize database first
    try:
        db_manager.create_tables()
        db_manager.run_migrations()
        print("✅ Database initialized for testing")
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False
    
    tests = [
        test_password_hashing,
        test_user_creation,
        test_user_authentication,
        test_session_management,
        test_role_permissions,
        test_document_user_association,
        test_default_admin_creation,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        print(f"\n{test.__name__}:")
        print("-" * 40)
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed} passed, {failed} failed")
    print("=" * 60)
    
    if failed == 0:
        print("🎉 All tests passed! Phase 2 is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
