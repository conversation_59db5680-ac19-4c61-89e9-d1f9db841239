# Document Management System (DMS) - Phase 2 Requirements
# Python 3.8+ required

# Core dependencies
# Note: tkinter is included with Python standard library

# Phase 2 dependencies
bcrypt>=4.0.0                    # Password hashing for user authentication

# Future phases will add:
# cryptography>=41.0.0             # Phase 4: File encryption
# matplotlib>=3.7.0                # Phase 5: Charts and reports
# requests>=2.31.0                 # Phase 6: Cloud integration
# google-api-python-client>=2.0.0  # Phase 6: Google Drive integration

# Development and testing (optional)
# pytest>=7.4.0
# pytest-cov>=4.1.0
