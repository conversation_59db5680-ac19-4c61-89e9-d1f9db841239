"""
Configuration settings for the Document Management System.
"""
import os
from pathlib import Path

# Application settings
APP_NAME = "Document Management System"
APP_VERSION = "1.0.0"

# Database settings
BASE_DIR = Path(__file__).parent.parent
DATABASE_PATH = BASE_DIR / "data" / "dms.db"
DATABASE_URL = f"sqlite:///{DATABASE_PATH}"

# File storage settings
DOCUMENTS_DIR = BASE_DIR / "documents"
ALLOWED_EXTENSIONS = {'.pdf', '.doc', '.docx', '.txt', '.jpg', '.jpeg', '.png', '.gif', '.bmp'}
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB

# UI settings
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800
THEME = "clam"  # Available themes: clam, alt, default, classic

# Search settings
SEARCH_RESULTS_PER_PAGE = 20

# Logging settings
LOG_LEVEL = "INFO"
LOG_FILE = BASE_DIR / "logs" / "dms.log"

def ensure_directories():
    """Create necessary directories if they don't exist."""
    directories = [
        DATABASE_PATH.parent,
        DOCUMENTS_DIR,
        LOG_FILE.parent
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)

def get_database_path() -> str:
    """Get the database path as string."""
    ensure_directories()
    return str(DATABASE_PATH)

def get_documents_dir() -> str:
    """Get the documents directory path as string."""
    ensure_directories()
    return str(DOCUMENTS_DIR)
