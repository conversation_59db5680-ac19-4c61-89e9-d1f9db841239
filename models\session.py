"""
Session management functionality for user authentication.
"""
import secrets
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any

from db.database import db_manager
from db.models import User, UserSession
from models.user import user_manager

logger = logging.getLogger(__name__)

class SessionManager:
    """Manages user sessions and authentication state."""
    
    def __init__(self):
        """Initialize session manager."""
        self.session_timeout_hours = 24
        self.current_session: Optional[UserSession] = None
        self.current_user: Optional[User] = None
    
    def create_session(self, user: User) -> Optional[UserSession]:
        """Create a new session for user."""
        try:
            # Generate secure session token
            session_token = secrets.token_urlsafe(32)
            
            # Calculate expiration time
            expires_at = datetime.now() + timedelta(hours=self.session_timeout_hours)
            
            # Create session record
            session = UserSession(
                user_id=user.id,
                session_token=session_token,
                created_at=datetime.now(),
                expires_at=expires_at,
                is_active=True
            )
            
            # Insert into database
            query = '''
                INSERT INTO user_sessions (user_id, session_token, created_at, expires_at, is_active)
                VALUES (?, ?, ?, ?, ?)
            '''
            params = (
                session.user_id, session.session_token, session.created_at,
                session.expires_at, session.is_active
            )
            
            session.id = db_manager.execute_insert(query, params)
            
            # Set as current session
            self.current_session = session
            self.current_user = user
            
            logger.info(f"Session created for user {user.username}")
            return session
            
        except Exception as e:
            logger.error(f"Error creating session: {e}")
            return None
    
    def get_session_by_token(self, session_token: str) -> Optional[UserSession]:
        """Get session by token."""
        try:
            query = "SELECT * FROM user_sessions WHERE session_token = ?"
            results = db_manager.execute_query(query, (session_token,))
            
            if results:
                return UserSession.from_dict(results[0])
            return None
            
        except Exception as e:
            logger.error(f"Error getting session by token: {e}")
            return None
    
    def validate_session(self, session_token: str) -> Optional[User]:
        """Validate session and return user if valid."""
        try:
            session = self.get_session_by_token(session_token)
            if not session or not session.is_valid():
                return None
            
            # Get user
            user = user_manager.get_user_by_id(session.user_id)
            if not user or not user.is_active:
                return None
            
            # Update current session and user
            self.current_session = session
            self.current_user = user
            
            return user
            
        except Exception as e:
            logger.error(f"Error validating session: {e}")
            return None
    
    def refresh_session(self, session_token: str) -> bool:
        """Refresh session expiration time."""
        try:
            session = self.get_session_by_token(session_token)
            if not session or not session.is_valid():
                return False
            
            # Update expiration time
            new_expires_at = datetime.now() + timedelta(hours=self.session_timeout_hours)
            
            query = "UPDATE user_sessions SET expires_at = ? WHERE session_token = ?"
            rows_affected = db_manager.execute_update(query, (new_expires_at, session_token))
            
            if rows_affected > 0:
                session.expires_at = new_expires_at
                self.current_session = session
                logger.info(f"Session refreshed for token {session_token[:8]}...")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error refreshing session: {e}")
            return False
    
    def invalidate_session(self, session_token: str) -> bool:
        """Invalidate a session (logout)."""
        try:
            query = "UPDATE user_sessions SET is_active = 0 WHERE session_token = ?"
            rows_affected = db_manager.execute_update(query, (session_token,))
            
            if rows_affected > 0:
                # Clear current session if it matches
                if (self.current_session and 
                    self.current_session.session_token == session_token):
                    self.current_session = None
                    self.current_user = None
                
                logger.info(f"Session invalidated for token {session_token[:8]}...")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error invalidating session: {e}")
            return False
    
    def cleanup_expired_sessions(self):
        """Clean up expired sessions."""
        try:
            query = '''
                UPDATE user_sessions 
                SET is_active = 0 
                WHERE expires_at < ? AND is_active = 1
            '''
            rows_affected = db_manager.execute_update(query, (datetime.now(),))
            
            if rows_affected > 0:
                logger.info(f"Cleaned up {rows_affected} expired sessions")
            
        except Exception as e:
            logger.error(f"Error cleaning up expired sessions: {e}")
    
    def get_user_sessions(self, user_id: int, active_only: bool = True) -> list:
        """Get all sessions for a user."""
        try:
            if active_only:
                query = '''
                    SELECT * FROM user_sessions 
                    WHERE user_id = ? AND is_active = 1 
                    ORDER BY created_at DESC
                '''
            else:
                query = '''
                    SELECT * FROM user_sessions 
                    WHERE user_id = ? 
                    ORDER BY created_at DESC
                '''
            
            results = db_manager.execute_query(query, (user_id,))
            return [UserSession.from_dict(row) for row in results]
            
        except Exception as e:
            logger.error(f"Error getting user sessions: {e}")
            return []
    
    def login(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """Login user and create session."""
        try:
            # Authenticate user
            user = user_manager.authenticate_user(username, password)
            if not user:
                return None
            
            # Create session
            session = self.create_session(user)
            if not session:
                return None
            
            return {
                'user': user,
                'session': session,
                'session_token': session.session_token
            }
            
        except Exception as e:
            logger.error(f"Error during login: {e}")
            return None
    
    def logout(self, session_token: str = None) -> bool:
        """Logout user and invalidate session."""
        try:
            token = session_token or (self.current_session.session_token if self.current_session else None)
            if not token:
                return False
            
            # Log audit event
            if self.current_user:
                user_manager.log_audit_event(
                    user_id=self.current_user.id,
                    action="LOGOUT",
                    details="User logged out"
                )
            
            # Invalidate session
            return self.invalidate_session(token)
            
        except Exception as e:
            logger.error(f"Error during logout: {e}")
            return False
    
    def get_current_user(self) -> Optional[User]:
        """Get current authenticated user."""
        return self.current_user
    
    def get_current_session(self) -> Optional[UserSession]:
        """Get current session."""
        return self.current_session
    
    def is_authenticated(self) -> bool:
        """Check if user is authenticated."""
        return (self.current_user is not None and 
                self.current_session is not None and 
                self.current_session.is_valid())
    
    def has_permission(self, permission: str) -> bool:
        """Check if current user has permission."""
        if not self.is_authenticated():
            return False
        return self.current_user.has_permission(permission)
    
    def require_permission(self, permission: str) -> bool:
        """Require specific permission (raises exception if not authorized)."""
        if not self.has_permission(permission):
            raise PermissionError(f"Permission required: {permission}")
        return True
    
    def create_default_admin(self) -> Optional[User]:
        """Create default admin user if no users exist."""
        try:
            # Check if any users exist
            users = user_manager.get_all_users(include_inactive=True)
            if users:
                return None
            
            # Create default admin
            admin_user = user_manager.create_user(
                username="admin",
                email="<EMAIL>",
                password="admin123",  # Should be changed on first login
                full_name="System Administrator",
                role="Admin"
            )
            
            if admin_user:
                logger.info("Default admin user created: admin/admin123")
                return admin_user
            
        except Exception as e:
            logger.error(f"Error creating default admin: {e}")
        
        return None

# Global session manager instance
session_manager = SessionManager()
